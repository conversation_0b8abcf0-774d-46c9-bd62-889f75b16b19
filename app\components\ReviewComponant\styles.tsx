import { StyleSheet } from 'react-native';
import { BaseColors } from '../../config/theme';
import { FontFamily } from '@config/typography';


export default StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    // alignItems: 'center',
    paddingTop: 20,
    paddingHorizontal: 10,
    paddingBottom: 10,
  },
  photoContainer: {
    marginBottom: 10,
  },
  dashedBorder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: BaseColors.secondaryBule, // Your desired border color
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 90 / 2,
  },
  text: {
    fontSize: 21,
    color: BaseColors.inputColor, // Gray color
    textAlign: 'center',
    paddingHorizontal: 30,
    fontFamily: FontFamily.OpenSansRegular,
  },
  inputTitle: {
    color: BaseColors.inputColor,
  },
  textInput: {
    color: BaseColors.inputColor,
  },
  codeText: {
    color: BaseColors.inputColor,
  },
  textInputContainer: {
    borderColor: 'transparent',
    // borderWidth: width * 0.005,
    // borderRadius: width * 0.02,
  },
  noErrorContainer: {
    // borderColor: BaseColors.white,
  },
  basicDetailView: {
    borderWidth: 1,
    borderColor: BaseColors.textInput,
    marginVertical: 5,
    paddingTop: 5,
    paddingHorizontal: 5,
    paddingBottom: 10,
    borderRadius: 10,
  },
  detailTxt: {
    fontSize: 21,
    fontWeight: '400',
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 2,
  },
  imgViewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mView: {
    marginTop: 15,
    marginBottom: 15,
  },
  nextBtnSty: {
    marginVertical: 20,
    marginHorizontal: 20,
  },
  otherDetailViewSty: {
    borderWidth: 1,
    borderColor: '#e5e5e5',
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingTop: 7,
    paddingBottom: 15,
    borderRadius: 10,
  },
  otherdetailTxtSty: {
    fontSize: 21,

    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  mtopSty: {
    marginTop: 15,
  },
  firstNameViewSty: {
    paddingHorizontal: 12,
  },
  mBottom: {
    marginBottom: 5,
  },
  optionsContainer: {
    // width: nWidth,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
  },
  nextButtonContainer :{
    padding: 15,

  },
});
