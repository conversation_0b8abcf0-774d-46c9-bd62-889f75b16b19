import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Platform,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import styles from './styles';

import {BaseColors} from '@config/theme';
import TextInput from '@components/UI/TextInput';
import {translate} from '@language/Translate';
import Geolocation from 'react-native-geolocation-service';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';
import BaseSetting from '@config/setting';
import DropdownList from '@components/DropDownList';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import userConfigActions from '@redux/reducers/userConfig/actions';
import Button from '@components/UI/Button';
import {isEmpty} from '@app/utils/lodashFactions';
import Toast from 'react-native-simple-toast';
import SliderComponant from '@components/SliderComponant';
import Header from '@components/Header';
import {isIOS} from '@app/utils/CommonFunction';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {getApiData} from '@app/utils/apiHelper';
import Placeautocomplete from '@components/Placeautocomplete';
import SubTagComponant from '@components/SubTagComponat';
import AnimatedView from '@components/AnimatedView';
import SwitchComponent from '@components/SwitchComponant';

interface DropdownItem {
  title: string;
}

const durationOptions: DropdownItem[] = [
  {title: 'Per hour'},
  {title: 'Per day'},
  {title: 'Per week'},
  {title: 'Per month'},
  {title: 'Per year'},
];
export default function FilterScreen(props: any) {
  const {navigation, route}: {navigation: any; route: any} = props;
  const refRBSheet = useRef<any>(null);
  const params = route?.params || {};
  const selectedTab = params?.selectedTab;
  const {setFilter, setHarborFilter} = userConfigActions;
  const dispatch = useDispatch();
  const {filterData, harborFilterData} = useSelector(
    (state: any) => state.userConfig,
  ); // Use your RootState type
  const fData =
    params?.type === 'myHarbor' ? harborFilterData || {} : filterData || {};

  console.log('fData ===>', fData);

  const [startTime, setStartTime] = useState<Date | undefined>(
    fData?.startTime,
  );
  const [endTime, setEndTime] = useState<Date | undefined>(
    fData?.endTime || undefined,
  );
  const [duration, setDuration] = useState<any>(fData?.duration || '');
  const [selectedTags, setSelectedTags] = useState<any>(fData?.skills || []);
  const [isFocused, setIsFocused] = useState(false);
  const [loc, setLoc] = useState(fData?.description || '');
  const [min, setMin] = useState(fData?.salaryStartPoint || '');
  const [max, setMax] = useState(fData?.salaryEndPoint || '');
  const [isAvailable, setIsAvailable] = useState(fData?.isavailable || false);
  const [flatRat, setFlatRat] = useState(fData?.flatRate || false);
  const [customOffer, setCustomOffer] = useState(fData?.customHarbor || false);
  const [verified, setVerified] = useState(fData?.verified || false);
  const [state, setState] = useState<any>({
    kmRadius: fData?.radiusRange || '',
    kmRadiusHigh: '',
    salaryStartRange: fData?.salaryStartPoint || '',
    salaryEndRange: fData?.salaryEndPoint || '',
    locationHistory: [],
    pagination: {},
  });
  // console.log('fData ==>', fData, state);
  const [startDate, setStartDate] = useState<Date | undefined>(
    fData?.startDate || undefined,
  );

  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    description: string;
  } | null>({
    latitude: fData?.latitude || '',
    longitude: fData?.longitude || '',
    description: fData?.location || fData?.description || '',
  });
  const [endDate, setEndDate] = useState<Date | undefined>(
    fData?.endDate || undefined,
  );
  // Assuming formattedStartDate is an ISO string, convert it to a Date object
  const formattedStartDatecheck: any = startDate;
  const minDate = new Date(formattedStartDatecheck); // Convert to Date object

  const handleDateChange = (date: Date) => {
    setStartDate(date);
  };

  useEffect(() => {
    if (loc) {
      setLocation((p: any) => ({...p, description: loc}));
    }
  }, [loc, setLocation]);
  // Function to filter options based on query
  const fetchLocationHistory = useCallback(async () => {
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.locationHistory,
        method: 'GET',
      });
      console.log('🚀 ~ fetchOptions ~ resp:', resp?.data);
      if (resp?.data && resp?.status) {
        setState((p: any) => ({
          ...p,
          locationHistory: resp?.data?.items || [],
          pagination: resp?.data?.pagination || {},
        }));
        //
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      // setLoader(false);
    } catch (e) {
      // setLoader(false);
      console.log('ERRR', e);
    }
  }, []);

  const getAddressDetails = () => {
    return Geolocation.getCurrentPosition(
      async (position: any) => {
        const {latitude, longitude} = position.coords;
        console.log('position ==>', position);

        // Call Google Reverse Geocoding API to get an address
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${BaseSetting.googleApiKey}`,
        );
        const data = await response.json();
        console.log('data ==>', data, data.results[0].formatted_address);
        if (data.results && data.results.length > 0) {
          const address = data.results[0].formatted_address;
          setLocation((p: any) => ({
            ...p,
            description: address,
            latitude: latitude,
            longitude: longitude,
          }));
          // Alert.alert('Current Location', address);
        } else {
          // Alert.alert('Error', 'Unable to fetch address.');
        }
        setLoading(false);
        // Toast.show(
        //   `Current Location Lat: ${latitude}, Lon: ${longitude}`,
        //   Toast.SHORT,
        // );
      },
      error => {
        setLoading(false);
        // Alert.alert('Error', error.message);
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  const getCurrentLocation = async () => {
    setLoading(true);

    // Check permissions
    const permission =
      Platform.OS === 'ios'
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

    const result = await check(permission);

    if (result === RESULTS.GRANTED) {
      // Permission granted
      getAddressDetails();
    } else if (result === RESULTS.DENIED) {
      // Request permission
      const requestResult = await request(permission);
      if (requestResult === RESULTS.GRANTED) {
        getCurrentLocation();
      } else {
        setLoading(false);
        // Toast.show(
        //   'Permission Denied Location permission is required to use this feature.',
        //   Toast.SHORT,
        // );
      }
    } else {
      setLoading(false);
      // Toast.show(
      //   'Permission Denied, Location permission is required to use this feature.',
      //   Toast.SHORT,
      // );
    }
  };

  const renderItem = ({item}: {item: any}) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => {
        console.log('item ==>', JSON.stringify(item));
        setLocation((p: any) => ({
          ...p,
          description: item?.location,
          latitude:
            item?.coordinates?.coordinates && item?.coordinates?.coordinates[1],
          longitude:
            item?.coordinates?.coordinates && item?.coordinates?.coordinates[0],
        }));
      }}>
      <View style={styles.locationIcon}>
        <Ionicons name="time-outline" size={24} color={BaseColors.primary} />
      </View>
      <View style={styles.itemText}>
        <Text style={styles.itemTitle} numberOfLines={2} ellipsizeMode="tail">
          {item?.location || ''}
        </Text>
        {/* <Text style={styles.itemDescription}>{item.description}</Text> */}
      </View>
    </TouchableOpacity>
  );

  const handleSubmit = (type: string) => {
    if (type === 'apply') {
      const data: any = {};
      if (!isEmpty(selectedTags)) {
        data.skills = selectedTags;
      }
      if (duration !== '') {
        data.duration = duration;
      }
      if (location?.description) {
        data.location = location?.description || '';
        data.lat = location?.latitude || fData?.latitude || fData?.lat || '';
        data.long =
          location?.longitude || fData?.longitude || fData?.long || '';
      }
      console.log('location ==>', location);
      if (startDate && endDate) {
        data.startDate = startDate;
        data.endDate = endDate;
      }
      if (state?.kmRadius) {
        data.radiusRange = state?.kmRadius;
      }
      if (min && max) {
        console.log('check salery point ');
        data.salaryStartPoint = min;
        data.salaryEndPoint = max;
      }
      if (isAvailable === true) {
        data.isavailable = isAvailable;
      }
      if (verified === true) {
        data.verified = verified;
      }
      if (customOffer === true) {
        data.customHarbor = customOffer;
      }
      if (flatRat === true) {
        data.flatRate = flatRat;
      }
      console.log('startTime ===>', startTime, endTime, endTime && startTime);

      if (startTime && endTime) {
        data.startTime = startTime;
        data.endTime = endTime;
      }
      console.log('data  ==>', data);
      if (params?.type === 'myHarbor') {
        dispatch(setHarborFilter(data) as any);
      } else {
        dispatch(setFilter(data) as any);
      }
      navigation.goBack();
    } else {
      if (params?.type === 'myHarbor') {
        dispatch(setHarborFilter({}) as any);
      } else {
        dispatch(setFilter({}) as any);
      }
      navigation.goBack();
    }
  };

  const handleComplete = useCallback((val: any, e: any) => {
    console.log('locatiomn ===<>', val);
    setLocation((p: any) => ({
      ...p,
      description: val?.description || '',
      latitude: e?.geometry?.location?.lat,
      longitude: e?.geometry?.location?.lng,
    }));

    console.log('🚀 ~ handleComplete ~ val:', val, e?.geometry?.location);
  }, []);

  const handleValueChange = useCallback(
    (newLow: any, newHigh: any) => {
      setState((p: any) => ({...p, kmRadius: newLow, kmRadiusHigh: newHigh}));
    },
    [setState],
  );
  const onChangeText = (e: any) => {
    setLoc(e);
  };
  const handleSalValueChange = useCallback(
    (newLow: any, newHigh: any) => {
      setState((p: any) => ({
        ...p,
        salaryStartRange: newLow,
        salaryEndRange: newHigh,
      }));
    },
    [setState],
  );

  const locationRef = useRef<any>(null);
  useEffect(() => {
    fetchLocationHistory();
    setLocation((p: any) => ({
      ...p,
      description: fData?.location || '',
      latitude: fData?.latitude || fData?.lat || '',
      longitude: fData?.longitude || fData?.long || '',
    }));
  }, []);

  const handleDone = (tags: string[]) => {
    setSelectedTags(tags); // Limit selectedTags to 3
    refRBSheet.current?.close();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}

      <Header
        leftIcon="back-arrow"
        title={translate('filter', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        nestedScrollEnabled={true}>
        <AnimatedView>
          <View style={styles.mainView}>
            {selectedTab === 'Seeker' ? (
              <View style={[styles.searchView, {marginTop: 20}]}>
                <View style={styles?.toggleViewSty}>
                  <View>
                    <Text style={[styles.radiousTxtSty, {marginTop: 0}]}>
                      {translate('availableNow', '')}
                    </Text>
                  </View>
                  <View>
                    <SwitchComponent
                      onValueChange={(p: any) => {
                        setIsAvailable(p);
                      }}
                      value={isAvailable}
                    />
                  </View>
                </View>
              </View>
            ) : null}
            <View style={[styles.searchView, {marginVertical: 20}]}>
              <View style={styles?.toggleViewSty}>
                <View>
                  <Text style={[styles.radiousTxtSty, {marginTop: 0}]}>
                    {translate('verified', '')}
                  </Text>
                </View>
                <View>
                  <SwitchComponent
                    onValueChange={(p: any) => {
                      setVerified(p);
                    }}
                    value={verified}
                  />
                </View>
              </View>
            </View>
            <View style={styles.searchView}>
              <View>
                {/* <TextInput
                value={search}
                onChange={(txt: any) => {
                  setSearch(txt);
                }}
                onFocus={() => setIsFocused(true)}
                onInputBlur={() => setIsFocused(false)}
                searchButton={true}
                placeholderText={translate('search', '')}
              /> */}

                <Placeautocomplete
                  refs={locationRef}
                  onAutoCompleteAddressSelect={(data, details) =>
                    handleComplete(data, details)
                  }
                  placeholder={translate('currentLocation', '')}
                  onChangeText={onChangeText}
                  location={location?.description || fData?.location || ''}
                  isDisable={false}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  title={translate('location', '')}
                  isError={false}
                  isErrorMsg={'locationErrTxt'}
                />
              </View>
              {loading && (
                <ActivityIndicator size="large" color={BaseColors.primary} />
              )}
              {isFocused && (
                <View style={styles.dropdown}>
                  <TouchableOpacity
                    style={styles.locationContainer}
                    onPress={getCurrentLocation}>
                    <View style={styles.locationIcon}>
                      <Ionicons
                        name="location-outline"
                        size={24}
                        color={BaseColors.primary}
                      />
                    </View>
                    <View style={styles.itemText}>
                      <Text style={styles.locationText}>
                        Use Current Location
                      </Text>
                    </View>
                  </TouchableOpacity>
                  {!isEmpty(state?.locationHistory) ? (
                    <Text style={styles.recentTitle}>Recent</Text>
                  ) : null}
                  {state?.locationHistory
                    ?.slice(0, 5) // Only take the first 5 items
                    .map((d: any) => {
                      return renderItem({item: d});
                    })}
                  {!isEmpty(state?.locationHistory) &&
                  state?.pagination?.isMore ? (
                    <TouchableOpacity style={styles.moreHistory}>
                      <Text style={styles.moreHistoryText}>
                        More from recent history
                      </Text>
                    </TouchableOpacity>
                  ) : null}
                </View>
              )}
              {!isFocused && (
                <SliderComponant
                  title={translate('radius', '')}
                  min={0}
                  max={250}
                  disableRange={true}
                  floating={false}
                  onValueChanged={handleValueChange}
                  low={state?.kmRadius || 50}
                  high={state?.kmRadiusHigh || 250}
                />
              )}
            </View>

            {!isFocused && (
              <View
                style={[styles.searchView, {marginVertical: 20, padding: 0}]}>
                <View style={styles?.skillViewSty}>
                  <Text style={styles?.selfTxtSty}>
                    {translate('addTags', '')}
                  </Text>
                  <View style={styles.mVertical}>
                    <Text style={styles.addskillTxtSty}>
                      {translate('addYourSkill', '')}
                    </Text>
                  </View>
                  <View>
                    {/* <AutoComplete
                    options={[]}
                    placeholder={translate('selecthere', '')}
                    // value={}
                    // onChange={}
                    // error={selectedtagErr.err}
                    // errorText={selectedtagErr.txt}
                    mandatory={true}
                    setSelectedTags={setSelectedTags}
                    selectedTags={selectedTags}
                    url={BaseSetting.endpoints.getSkills}
                  /> */}
                    <SubTagComponant
                      refRBSheet={refRBSheet}
                      setOpenBottomSheet={() => {}}
                      Description=""
                      type="job"
                      cancelProp={() => {}}
                      onClick={handleDone} // Pass the callback
                      doneProp="Done"
                      showDescription={false}
                      deleteSty={false}
                      showCancelbutton={false}
                      options={[]} // Pass your options here
                      setSelectedLanguage={() => {}}
                      selectedLanguage={''}
                      selectedTags={selectedTags} // Pass the actual selectedTags
                      setSelectedTags={setSelectedTags}
                    />
                  </View>
                </View>
              </View>
            )}
            {!isFocused && (
              <View style={[styles.searchView, {padding: 0}]}>
                <View style={styles?.skillViewSty}>
                  <Text style={styles?.selfTxtSty}>
                    {translate('Duration', '')}
                  </Text>

                  <View style={styles.dropDownView}>
                    <DropdownList
                      data={durationOptions}
                      selectedValue={duration}
                      onSelect={selectedValue => {
                        setDuration(selectedValue); // Update the duration state
                      }}
                      placeholder={translate('selectDuration', '')}
                      mandatory={true}
                    />
                  </View>
                </View>
              </View>
            )}

            {!isFocused && (
              <View
                style={[styles.searchView, {marginVertical: 20, padding: 0}]}>
                <View style={styles?.skillViewSty}>
                  <Text style={styles?.selfTxtSty}>
                    {translate('totalPay', '')}
                  </Text>

                  {/* <SliderComponant
                  title="Range"
                  min={1}
                  max={50}
                  // initialLowValue={0}
                  // initialHighValue={40}
                  disableRange={false}
                  floating={true}
                  onValueChanged={handleSalValueChange}
                  low={state?.salaryStartRange || 0}
                  high={state?.salaryEndRange || 40}

                /> */}

                  <View style={styles.radiousView}>
                    <View style={styles.insideView}>
                      <TextInput
                        value={min}
                        title={translate('Min', '')}
                        onChange={(value: any) => {
                          setMin(value);
                        }}
                        placeholderText={translate('Min', '')}
                        keyBoardType="number-pad"
                      />
                      {/* <Text style={styles.insideTXt}>
                      {state?.salaryStartRange || 0}
                    </Text> */}
                    </View>
                    <View style={styles.insideView}>
                      <TextInput
                        value={max}
                        title={translate('Max', '')}
                        onChange={(value: any) => {
                          setMax(value);
                        }}
                        placeholderText={translate('Max', '')}
                        keyBoardType="number-pad"
                      />
                    </View>
                  </View>
                </View>
              </View>
            )}
            <View style={[styles.searchView]}>
              <View>
                <Text style={styles?.selfTxtSty}>{translate('dates', '')}</Text>
              </View>
              <View style={styles.dateViewSty}>
                <View style={styles.rowSpaceBetween}>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      Date={true}
                      selectedDate={startDate}
                      onDateChange={handleDateChange}
                      title={translate('startDate', '')}
                      datetimemodal={translate('startDate', '')}
                    />
                  </View>
                  <View
                    style={[
                      styles.inputHalfWidth,
                      {paddingBottom: isIOS() ? 15 : 0},
                    ]}>
                    <TextInput
                      iseditable={startDate ? true : false} // Editable only if startDate is selected
                      Date={true}
                      selectedDate={endDate}
                      onDateChange={(date: any) => {
                        setEndDate(date); // Set the start date when changed
                      }}
                      title={translate('endDate', '')}
                      minDate={minDate}
                      datetimemodal={translate('endDate', '')}
                    />
                  </View>
                </View>
              </View>
            </View>

            <View style={[styles.searchView, {marginTop: 20}]}>
              <View>
                <Text style={styles?.selfTxtSty}>{translate('Time', '')}</Text>
              </View>
              <View style={[styles.dateViewSty]}>
                <View style={styles.rowSpaceBetween}>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      Date={true}
                      selectedDate={startTime}
                      onDateChange={(time: Date) => {
                        setStartTime(time);
                      }}
                      title={translate('startTime', '')}
                      dateType="time"
                      Time={true}
                      datetimemodal="Start Time"
                    />
                  </View>
                  <View
                    style={[
                      styles.inputHalfWidth,
                      {paddingBottom: isIOS() ? 15 : 0},
                    ]}>
                    <TextInput
                      Date={true}
                      selectedDate={endTime}
                      onDateChange={(time: Date) => {
                        setEndTime(time);
                      }}
                      iseditable={startTime ? true : false} // Editable only if startDate is selected
                      title={translate('endTime', '')}
                      dateType="time"
                      Time={true}
                      datetimemodal="End Time"
                    />
                  </View>
                </View>
              </View>
            </View>
            {selectedTab === 'Employer' ? (
              <>
                <View style={[styles.searchView, {marginTop: 20}]}>
                  <View style={styles?.toggleViewSty}>
                    <View>
                      <Text style={[styles.radiousTxtSty, {marginTop: 0}]}>
                        {translate('customOffer', '')}
                      </Text>
                    </View>
                    <View>
                      <SwitchComponent
                        onValueChange={(p: any) => {
                          setCustomOffer(p);
                        }}
                        value={customOffer}
                      />
                    </View>
                  </View>
                </View>
                <View style={[styles.searchView, {marginTop: 20}]}>
                  <View style={styles?.toggleViewSty}>
                    <View>
                      <Text style={[styles.radiousTxtSty, {marginTop: 0}]}>
                        {translate('flatRate', '')}
                      </Text>
                    </View>
                    <View>
                      <SwitchComponent
                        onValueChange={(p: any) => {
                          setFlatRat(p);
                        }}
                        value={flatRat}
                      />
                    </View>
                  </View>
                </View>
              </>
            ) : null}
          </View>
        </AnimatedView>
      </KeyboardAwareScrollView>
      <View style={styles?.btnSty}>
        <View style={styles?.btnView}>
          <Button
            onPress={() => {
              handleSubmit('clear');
            }}
            type="outlined">
            {translate('clear', '')}
          </Button>
        </View>
        <View style={styles?.btnView}>
          <Button
            onPress={() => {
              handleSubmit('apply');
            }}
            type="text">
            {translate('apply', '')}
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}
