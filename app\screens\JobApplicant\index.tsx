/* eslint-disable react-native/no-inline-styles */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  LayoutChangeEvent,
  Platform,
  RefreshControl,
  ScrollView,
  Share,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import { BaseColors, BaseStyles } from '@config/theme';
import EmployarCard from '@components/EmployerCard';
import Entypo from 'react-native-vector-icons/Entypo';
import { FontFamily } from '@config/typography';
import FastImage from 'react-native-fast-image';
import { Images } from '@config/images';
import { Menu, MenuItem } from 'react-native-material-menu';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import { translate } from '@language/Translate';
import { isArray, isEmpty, lowerCase } from '@app/utils/lodashFactions';
import PaymentBreakdown from '@components/PaymentBreakdown';
import Button from '@components/UI/Button';
import AlertModal from '@components/AlertModal';
import socketAction from '@redux/reducers/socket/actions';
import FIcon from 'react-native-vector-icons/FontAwesome';

import {
  capitalizeFirstLetter,
  capitalizeFirstLetterOfEachWord,
  convertToCamelCase,
  getTimeAgo,
  imagePattern,
  resetStack,
  updateReview,
} from '@app/utils/CommonFunction';
import StepIndicator from '@components/StepIndicator';
import LanguageModal from '@components/LanguageModal';
import SeekerCard from '@components/SeekerCard';
import Toast from 'react-native-simple-toast';
import { useFocusEffect } from '@react-navigation/native';
import ReviewRatingComponant from '@components/ReviewRatingComponant';
import authActions from '@redux/reducers/auth/actions';
import AnimatedView from '@components/AnimatedView';
import NoRecord from '@components/NoRecord';
import CustomOfferModal from '@components/CustomOfferModal';
import moment from 'moment';
import { updateJobStatus } from './ApiFuntions';
import CounterOfferCard from '@components/SeekerCard/CounterOfferCard';
import {
  initPaymentSheet,
  presentPaymentSheet,
} from '@stripe/stripe-react-native';
import CounterHistory from '@screens/CounterHistory/Index';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import UserConfigActions from '@redux/reducers/userConfig/actions';
import { EventRegister } from 'react-native-event-listeners';

const { emit, setSelectedRoom } = socketAction;
const { setUpdateJobData } = UserConfigActions;
const { width } = Dimensions.get('screen');

const stData = {
  customOfferModal: false,
  confirmationModal: false,
  counterOfferModal: false,
  loader: false,
  type: '',
  applicant: {},

  // counterHistory: [],
  customOfferLoader: false,
  counterOfferLoader: false,
};

export default function JobApplicant({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {
  const { params } = route;
  const [refreshing, setRefreshing] = useState(false);
  const { useAppSelector } = useRedux();
  const dispatch = useAppDispatch();

  const { updateJobData } = useAppSelector((state: any) => state.userConfig);
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const IOS = Platform.OS === 'ios';

  const refRBSheet = useRef<any>(null);
  const [openBottomSheet, setOpenBottomSheet] = useState<boolean>(false);
  const [openlogoutBottomSheet, setOpenLogoutBottomSheet] =
    useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });

  const jobId = params?.jobID || params?.jobId;

  const type = params?.type;
  const notificationId = params?.notificationId;
  const jobDetailId =
    type === 'notification' ? notificationId : jobId?.id || jobId;
  const [loader, setLoader] = useState<any>(false);
  const [cancellationLoader, setCancellationLoader] = useState<any>(false);
  const [listLoader, setListLoader] = useState<boolean>(false);
  const [jobDetail, setJobDetail] = useState<any>('');
  const isEmployer = userData?.id === jobDetail?.userId;
  const [jobapplicantList, setjobapplicantList] = useState<any>([]);
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
  const [openDeleteModalLoader, setOpenDeleteModalLoader] =
    useState<boolean>(false);
  const [state, setState] = useState<any>({
    customOfferModal: false,
    loader: false,
    counterOfferModal: false,
    type: '',
    confirmationModal: false,
    applicant: {},

    counterHistory: [],
    customOfferLoader: false,
    counterOfferLoader: false,
  });
  const [saveLoader, setSaveLoader] = useState('');
  const [paymentInProcess, setPaymentInProcess] = useState(false);
  const { customOfferModal, counterHistory, counterOfferModal } = state;
  console.log('counterHistory ====>', jobDetail);

  // const [click, setClick] = useState<boolean>(false);
  // const [checkBoxErr, setCheckBoxErr] = useState(false);
  // const [checkBoxErrTxt, setCheckBoxErrTxt] = useState('');

  const jobStatus = lowerCase(jobDetail?.isApplied?.status);

  // Split applicants into groups of two
  const groupedApplicants =
    jobapplicantList &&
    jobapplicantList?.reduce((result: any, applicant: any, index: number) => {
      if (index % 1 === 0) {
        result.push([applicant]);
      } else {
        result[result?.length - 1].push(applicant);
      }
      return result;
    }, []);

  const [cAlert, setCAlert] = useState({
    message: translate('appliedNotification', ''),
    showAlert: false,
    image: Images.usaPng,
  });
  const timeAgo = getTimeAgo(jobDetail?.createdAt);

  const isSeeker = userData?.id !== jobDetail?.userId;

  const isCustomOffer = jobDetail?.isPriceOptional;
  const isCustomHarbor = jobDetail?.type === 'custom';

  const employerReject = !isEmpty(jobapplicantList)
    ? isEmployer &&
      jobDetail?.jobStatus === 'Declined' &&
      jobapplicantList &&
      (jobapplicantList[0]?.offerBy === 'seeker' ||
        jobapplicantList[0]?.isAccepted)
    : false;

  const seekerReject = !isEmpty(jobapplicantList)
    ? isEmployer &&
      jobDetail?.jobStatus === 'Declined' &&
      jobapplicantList &&
      jobapplicantList[0]?.offerBy === 'employer'
    : false;

  console.log(
    'employerReject ===>',
    employerReject,
    seekerReject,
    counterHistory,
  );

  const declinedView = isCustomHarbor
    ? isCustomOffer
      ? employerReject || seekerReject
      : isEmployer && jobDetail?.jobStatus === 'Declined'
    : false;

  const status =
    jobDetail?.approvedApplicant?.status && !isSeeker
      ? String(jobDetail?.approvedApplicant?.status).toLowerCase()
      : String(jobDetail?.isApplied?.status).toLowerCase();

  const approvedStatus = status === 'approved' || status === 'completed';
  const isNotApplied =
    jobDetail?.isApplied === null || jobDetail?.isApplied?.status === 'Pending';

  const isCustomCounterNotStarted =
    isCustomHarbor &&
    isEmpty(counterHistory) &&
    isCustomOffer &&
    isSeeker &&
    status === 'pending';
  const isCustomHarborOnly =
    isCustomHarbor &&
    isEmpty(counterHistory) &&
    isSeeker &&
    status === 'pending' &&
    jobDetail?.isDirectApplied;

  const acceptApplyView = isCustomHarbor
    ? isCustomHarborOnly
    : jobDetail?.isApproved !== 1 && jobDetail?.isDirectApplied;
  const rejectBtnView = isCustomHarbor
    ? isCustomHarborOnly
    : isSeeker && isCustomHarbor && jobDetail?.status === 'pending';

  const sendCustomOfferView = isCustomHarbor
    ? isCustomCounterNotStarted
    : isCustomOffer && isNotApplied && isSeeker;

  // const isCustomHarborWithPending = !isEmpty(jobapplicantList) && jobapplicantList[0] ? String(jobapplicantList[0]?.userJob?.status).toLowerCase() === 'pending' : false;

  const getCounterList = async (id: any, isEmp?: boolean | string) => {
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.counterOfferList,
        method: 'POST',
        data: {
          jobId: id,
          userId: userData?.id,
          userType: isEmp ? 'employer' : 'seeker',
        },
      });
      console.log('counterHistory ===>', resp, {
        jobId: id,
        userId: userData?.id,
        userType: isEmp ? 'employer' : 'seeker',
      });
      if (resp?.status) {
        setState({
          ...state,
          counterHistory: resp?.data,
          customOfferModal: false,
          counterOfferModal: false,
          customOfferLoader: false,
          confirmationModal: false,
          applicant: {},
          job: {},
          type: '',
          clearModalState: true,
        });
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
    } catch (e) {
      // Toast.show(e?.message || translate('err', ''), Toast.SHORT);
      console.log('called catch ==>', e?.message);
    }
  };

  const getList = useCallback(
    async (lType?: string = '', data?: any) => {
      console.log('jobDetailId || jobId ===>', jobDetailId || jobId, lType);
      if (lType === 'refresh') {
        setRefreshing(true);
      } else if (lType !== 'Approved' && lType !== 'silently') {
        setListLoader(true);
      }
      try {
        const resp = await getApiData({
          endpoint:
            BaseSetting.endpoints.jobDetail + `/${jobDetailId || jobId}`,
          method: 'GET',
        });

        console.log('resp ==>', resp, state?.type);
        if (resp?.data && resp?.status) {
          // setState({
          //   ...state,
          //   customOfferModal: false,
          //   counterOfferModal: false,
          //   customOfferLoader: false,
          //   applicant: false,
          //   job: {},
          //   clearModalState: true,
          // });
          if (resp?.data?.paymentStatus === 'processing' || resp?.data?.paymentStatus === 'paid') {
            setPaymentInProcess(false);
          }

          if (lType === 'Approved') {
            setTimeout(async () => {
              await initializePaymentSheet(
                state?.applicant?.id || data?.id,
                resp?.data,
              );
            }, 800);
          }
          setJobDetail(resp?.data); // Update list with new data

          getCounterList(resp?.data?.id, userData?.id === resp?.data?.userId);
          if (userData?.id === resp?.data?.userId) {
            jobApplicantList(lType);
          }
        }
        if (lType === 'refresh') {
          setRefreshing(false);
        } else {
          setListLoader(false);
        }
      } catch (error) {
        if (lType === 'refresh') {
          setRefreshing(false);
        } else {
          setListLoader(false);
        }
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    },
    [jobId, jobDetail],
  );

  const handleRefresh = (data?: any, type?: string) => {
    // setLoader(true);
    if (type === 'payment') {
      setPaymentInProcess(true);
    }
    getList('silently');
  };

  const updateJobDataViaNotification = (data: any) => {
    console.log('addEventListener data ===>', data, data?.jobId, jobId, jobDetail, data?.jobId == jobId);
    if (data?.jobId == jobId) {
      getList('silently');
    }
  };

  useEffect(() => {
    EventRegister.addEventListener('job_update', (data: any) => {
      console.log('addEventListener data ===>', data, data?.jobId, jobDetail?.id, data?.jobId === jobDetail?.id);
      updateJobDataViaNotification(data);
    });
  }, []);

  const [expandedReviews, setExpandedReviews] = useState<{
    [key: string]: boolean;
  }>({});
  const [isTruncated, setIsTruncated] = useState<{[key: string]: boolean}>({});

  const lineHeight = 20; // Replace this with your actual line height from styles

  const toggleExpansion = (reviewId: string | number) => {
    setExpandedReviews(prevState => ({
      ...prevState,
      [reviewId]: !prevState[reviewId],
    }));
  };

  const handleLayout = (
    event: LayoutChangeEvent,
    reviewId: string | number,
  ) => {
    const { height } = event.nativeEvent.layout;
    const maxHeight = 3 * lineHeight; // Height for 3 lines

    setIsTruncated(prevState => ({
      ...prevState,
      [reviewId]: height > maxHeight, // Check if height exceeds 3 lines
    }));
  };

  const fetchPaymentSheetParams = async (
    amount = 20,
    id = null,
    jobItem: any = null,
  ) => {
    console.log('amount ===>', amount, id);
    let data: {
      amount?: number;
      jobId?: number;
      currency?: string;
      seekerId?: any;
      timezone?: any;
    } = {};
    if (Number(amount) > 0) {
      data.amount = amount;
      data.jobId = jobDetail?.id || jobItem?.id;
      data.currency = 'USD';
      data.seekerId = id;
      data.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    console.log('🚀 ~ fetchPaymentSheetParams ~ data:', data);
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getStripIntentId,
      method: 'POST',
      data: data,
    });
    console.log('🚀 ~ fetchPaymentSheetParams ~ resp:', resp);
    if (!resp?.status) {
      Toast.show(resp?.message || translate('error'), Toast.LONG);
    }

    const { paymentIntent, ephemeralKey, customer } = resp?.data || {};

    return {
      paymentIntent,
      ephemeralKey,
      customer,
    };
  };

  const initializePaymentSheet = async (id?: any, item?: any) => {
    console.log(
      'paymentIntent ===>',
      id,
      jobDetail?.totalEstimateCharge,
      item?.totalEstimateCharge,
    );
    if (isEmployer) {
      const { paymentIntent, ephemeralKey, customer } =
        await fetchPaymentSheetParams(
          jobDetail?.totalEstimateCharge || item?.totalEstimateCharge,
          id,
          item,
        );

      const { error } = await initPaymentSheet({
        merchantDisplayName: 'The Harbor App',
        customerId: customer,
        customerEphemeralKeySecret: ephemeralKey,
        paymentIntentClientSecret: paymentIntent,
        // Set `allowsDelayedPaymentMethods` to true if your business can handle payment
        //methods that complete payment after a delay, like SEPA Debit and Sofort.
        allowsDelayedPaymentMethods: true,
        // defaultBillingDetails: {
        //   name: 'Jane Doe',
        // },
        googlePay: {
          merchantCountryCode: 'US',
          testEnv: false, // use test environment
        },
        applePay: {
          merchantCountryCode: 'US',
        },
        returnURL: 'com.harbor.app://stripe-redirect',
      });
      if (!error) {
        openPaymentSheet();
        // setLoading(true);
      }
    }
  };

  const openPaymentSheet = async () => {
    console.log('🚀 ~ openPaymentSheet ~ error:');
    const { error } = await presentPaymentSheet();
    console.log('🚀 ~ openPaymentSheet ~ error:', error);

    if (error) {
      Alert.alert(`Error code: ${error.code}`, error.message);
    } else {
      // updateJobStatus('Approved');
      setPaymentInProcess(true);
      getList();
      // Alert.alert('Success', 'Your order is confirmed!');
    }
  };

  const handleClick = useCallback(
    async (item: any, sType: string) => {
      console.log('sType ===>', sType, item);
      // return false;
      if (item?.counterOfferId) {
        if (sType === 'pay') {
          await initializePaymentSheet(item?.id, item);
          return false;
        }
        setState((p: any) => ({
          ...p,
          counterOfferModal: true,
          applicant: item,
          type: sType,
          job: jobDetail,
        }));
      } else {
        if (sType === 'Approved') {
          await initializePaymentSheet(item?.id);
          // navigation.navigate('JobDetailScreen', { applicant: state?.applicant, type, job: jobDetail });
          return false;
        }
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: item,
          type: sType,
          job: jobDetail,
        }));
        return false;
      }
      // if (type === 'Approved') {
      // navigation.navigate('JobDetailScreen', { applicant: item, type, job: jobDetail });
      // } else {
      //   console.log('called ---->', item);
      //   setState((p: any) => ({
      //     ...p,
      //     counterOfferModal: true,
      //     applicant: item,
      //     type: sType,
      //     job: jobDetail,
      //   }));
      // }
    },
    [jobDetail],
  );

  const renderApplicantCard = ({ item, index }: {item: any; index: any}) => (
    <View
      style={{
        width: width / 1.1,
        marginLeft: index === 0 ? 0 : 10,
        pointerEvents: 'auto',
      }}>
      <CounterOfferCard
        item={item}
        type={'applicants'}
        onActionClick={handleClick}
        handleUpdateJob={handleRefresh}
        applicantsLoader={false}
        navigation={navigation}
        jobDetail={{ ...jobDetail, paymentProcessing: paymentInProcess ? true : false }}
        buttons={!paymentInProcess}
      />
    </View>
  );

  const [visible, setVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const handleScroll = (event: any) => {
    const slideIndex = Math.ceil(event.nativeEvent.contentOffset.x / width);
    setActiveIndex(slideIndex);
  };

  const clickToShare = () => {};

  const hideMenu = () => setVisible(false);
  const clickEdit = () => {
    navigation.navigate('JobPosting', {
      jobID: jobDetail,
      edit: 'edit',
      getList: getList,
    });
    setVisible(false);
  };
  const clickReview = () => {
    navigation.navigate('ReviewScreen', {
      jobID: jobDetail,
    });
    setVisible(false);
  };
  const clickDelete = () => {
    setVisible(false);
    setOpenDeleteModal(true);
    setOpenLogoutBottomSheet(true);
    setTimeout(() => {
      refRBSheet?.current?.open?.();
    }, 1000);
  };
  const openEmployerProfile = () => {
    navigation.navigate('ApplicantDetails', {
      userId: jobDetail?.userId,
      type: 'viewEmployer',
      reviewType: 'reviewbyEmployer',
    });
    setVisible(false);
  };
  const openChat = () => {
    handleChatNavigation('simpleChat');
    setVisible(false);
  };

  const openReport = () => {
    navigation.navigate('ContactUs', {
      reportType: 'report',
      jobId: jobDetailId,
      reportId: jobDetail?.referenceNumber,
    });
    setVisible(false);
  };

  const showMenu = () => setVisible(true);

  const handlePost = async (type: string) => {
    setLoader(true);
    const newObj = {
      jobId: jobDetail?.id,
      userId: jobDetail?.userId,
      status: 'Applied',
    };
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateuserJob,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        console.log('abss');
        handleUpdateJob();
        setCAlert({
          message: translate('appliedNotification', ''),
          showAlert: true,
          image: Images.usaPng,
        });

        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      } else {
        setLoader(false);

        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);

      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const validation = (type: any) => {
    if (userData?.isProfileSet === false) {
      // Toast.show(translate('addProfileDetail', ''));
      setModalOpen((p: any) => ({ ...p, confirmationModal: true }));
      return false;
    }
    handlePost(type);
  };
  const handleCancelPost = async (loaderType?: any = true) => {
    setCancellationLoader(true);
    setLoader(loaderType);
    const newObj = {
      jobId: jobDetail?.id,
      status: 'Declined',
    };

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.cancelApplication,
        method: 'DELETE',
        data: newObj,
      });
      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        refRBSheet?.current?.close();
        setCancellationLoader(false);

        getList();
      } else {
        setCancellationLoader(false);

        setLoader(false);
        refRBSheet?.current?.close();
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }
      setLoader(false);
      refRBSheet?.current?.close();
    } catch (err) {
      setCancellationLoader(false);

      setLoader(false);

      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };
  const jobApplicantList = async (type?: string) => {
    const newObj = {
      jobId: jobDetailId,
      page: 1,
      limit: 10,
    };
    if (type !== 'silently') {
      setListLoader(true);
    }

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.listApplicants,
        method: 'GET',
        data: newObj,
      });

      if (res?.status === true) {
        console.log('res ==>', res?.data?.items);
        // Set the job applicant list
        setjobapplicantList(res?.data?.items || []);
        setListLoader(false);
      } else {
        // Show error message for unauthorized action
        setjobapplicantList([]); // Ensure the list is empty
      }
      setListLoader(false);
    } catch (err) {
      // Handle API errors
      console.error('Error fetching applicants:', err);
      setjobapplicantList([]); // Clear the list to prevent crashes
      setListLoader(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      getList();
    }, []), // Dependency array ensures the function updates if dependencies change
  );

  useEffect(() => {
    if (updateJobData) {
      getList();
      setPaymentInProcess(false);
      dispatch(setUpdateJobData(false));
    }
    return () => {};
  }, [updateJobData]);

  const deleteFile = async () => {
    setOpenDeleteModalLoader(true);
    try {
      const url = BaseSetting.endpoints.deleteJob + `/${jobDetail?.id}`;
      const resp = await getApiData({
        endpoint: url,
        method: 'DELETE',
      });
      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);
        setOpenDeleteModalLoader(false);
        navigation.goBack();
      } else {
        setOpenDeleteModalLoader(false);
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    } catch (err) {
      setOpenDeleteModalLoader(false);
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    }
  };

  const [activeStep, setActiveStep] = useState(1);
  const steps = [
    translate('Applied', ''),
    translate('Approved', ''),
    translate('Completed', ''),
    translate('Reviewed', ''),
  ];

  const goToStep = (step: number) => {
    setActiveStep(step);
  };

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      getList('refresh');
    }
  }, [loader]);

  const handleChatNavigation = (type?: string) => {
    const isSimpleChat = type === 'simpleChat';
    const userId = jobDetail?.userId || '';
    const applicantId = isSimpleChat
      ? userData?.id
      : jobDetail?.isApplied
        ? jobDetail?.isApplied?.userId
        : jobDetail?.approvedApplicant?.userId;
    const jobId = jobDetail?.id || '';

    setLoader('chat');
    dispatch(
      emit('create_room', { userId, applicantId, jobId }, (res: any) => {
        const roomId = res?.roomId;
        const op = {
          userId: userData?.id || '',
          jobId: jobId,
          roomId,
        };
        dispatch(
          emit('get_single_chat', op, (singleRes: any) => {
            console.log('Read=====>', singleRes);
            setLoader(false);
            dispatch(
              setSelectedRoom({
                ...res,
                ...singleRes?.data,
              }),
            );
            navigation.navigate('ChatDetails', {
              userInfo: {
                ...res,
                ...singleRes?.data,
              },
            });
          }) as any,
        );
      }) as any,
    );
  };

  const handleUpdateJob = async () => {
    if (!userData?.id) {
      console.log('Error: userId is undefined');
      // Toast.show('User ID is missing. Please try again.', Toast.BOTTOM);
      return;
    }
    const slug = 'apply_for_job'; // Define the slug
    const { status, badgeInfo } = await updateReview(
      slug,
      userData.id,
      jobDetailId,
    );

    if (status) {
      if (badgeInfo) {
        const updatedUserData = {
          ...userData, // Keep all other properties
          badgeInfo, // Update badgeInfo
        };

        dispatch(authActions.setUserData(updatedUserData) as any);
        console.log(
          'User data updated successfully with new badgeInfo:',
          badgeInfo,
        );
      }
    } else {
      console.log('Update failed');
    }
  };

  const onSubmit = useCallback(
    async (values: any, type?: string) => {
      const isEdit = state?.type === 'edit';
      if (state?.applicant?.counterOfferId || type === 'custom') {
        if (state?.type === 'Approved' || state?.type === 'Declined') {
          const url = BaseSetting.endpoints.updateCounterOfferEdit;
          const apiData = {
            counterOfferId: state?.applicant?.counterOfferId || null,
            userId: userData?.id,
            jobId: jobDetail?.id || null,
            userType:
              state?.applicant?.offerBy === 'seeker' ? 'employer' : 'seeker',
            status: String(state?.type).toLowerCase(),
          };
          console.log('onsubmit called ==>', state?.applicant);
          // return false;
          try {
            setState((p: any) => ({ ...p, customOfferLoader: true }));
            const response = await getApiData({
              endpoint: url,
              data: apiData,
              method: 'POST',
            });

            if (response?.status) {
              // setState({
              //   ...state,
              //   customOfferModal: false,
              //   confirmationModal: false,
              //   counterOfferModal: false,
              //   customOfferLoader: false,
              //   type: '',
              //   applicant: {},
              //   job: {},
              // });
              console.log('response Approved ===>', response);
              getList(state?.type, state?.applicant);
              if (response?.message) {
                Toast.show(response?.message, Toast.SHORT);
              }
            } else {
              setState({
                ...state,
                customOfferLoader: false,
              });
              Toast.show(response?.message || translate('err'), Toast.SHORT);
            }
          } catch (error) {
            console.error('Error submitting form:', error);
          }
        } else {
          setState(p => ({ ...p, customOfferLoader: true }));
          console.log(
            '🚀 ~ handleSubmit values:',
            values,
            jobDetail?.isFlatRate,
          );
          // duration must of following :perHour,perDay,perWeek,perMonth"
          const payload: any = {
            jobId: jobDetail?.id,
            finalPrice: values?.salaryAmount,
            duration:
              values?.duration === 'Flat Rate' ||
              (jobDetail?.isFlatRate && values?.flatRate)
                ? undefined
                : convertToCamelCase(values?.duration),
            startTime: values?.startTime
              ? moment(values.startTime).format('hh:mm A')
              : '',
            endTime: values?.endTime
              ? moment(values.endTime).format('hh:mm A')
              : '',
            startDate: values?.startDate
              ? moment(values.startDate).format('MM/DD/YYYY')
              : '',
            endDate: values?.endDate
              ? moment(values.endDate).format('MM/DD/YYYY')
              : '',
            message: values?.msg || '',
            sendBy:
              state?.type === 'counter'
                ? isEmployer
                  ? userData?.id
                  : state?.applicant?.receiveBy
                : userData?.id,
            receiveBy:
              state?.type === 'counter'
                ? isEmployer
                  ? state?.applicant?.id
                  : state?.applicant?.sendBy
                : isEmployer
                  ? state?.applicant?.id
                  : jobDetail?.userId,
            // sendBy: isEmployer ? userData?.id : state?.applicant?.receiveBy,
            // receiveBy: isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy,
            offerBy: userData?.id == jobDetail?.userId ? 'employer' : 'seeker',
          };
          if (isEdit) {
            payload.counterOfferId = state?.applicant?.counterOfferId || null;
            payload.userId = userData?.id;
            payload.userType =
              userData?.id == jobDetail?.userId ? 'employer' : 'seeker';
          }

          const endPoint = isEdit
            ? BaseSetting.endpoints.createCounterOfferEdit
            : BaseSetting.endpoints.createOffer;
          try {
            const response = await getApiData({
              endpoint: endPoint,
              data: payload,
              method: 'POST',
            });

            console.log('response ==>', payload, response);

            if (response?.status) {
              // setState({ ...state, customOfferModal: false, counterOfferModal: false, customOfferLoader: false, applicant: false, job: {} });
              getList();
              Toast.show(response?.message, Toast.SHORT);
            } else {
              setState({ ...state, customOfferLoader: false });
              Toast.show(response?.message || translate('error'), Toast.SHORT);
            }
          } catch (error) {
            setState({ ...state, customOfferLoader: false });
            console.error('Error submitting form:', error);
          }
        }
        // return false;
      } else if (type === 'Approved') {
        navigation.navigate('JobDetailScreen', {
          applicant: state?.applicant,
          type,
          job: jobDetail,
        });
      } else {
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: state?.applicant,
          type,
          job: jobDetail,
        }));
      }
    },
    [customOfferModal, state, counterOfferModal, jobDetail],
  ); // Only depend on jobId to avoid unnecessary re-creations

  const handleOnClose = useCallback(() => {
    setState({ ...state, ...stData });
  }, [state]);

  const handleSave = async (type: any = '') => {
    setSaveLoader(
      type === 'user' ? jobDetail?.approvedApplicant?.userId : jobDetail?.id,
    );
    let data: any = {};
    try {
      if (type === 'user') {
        data = { userId: jobDetail?.approvedApplicant?.userId };
      } else {
        data = { jobId: jobDetail?.id };
      }
      const resp = await getApiData({
        endpoint:
          type === 'user'
            ? BaseSetting.endpoints.seekerSave
            : BaseSetting.endpoints.saveCard,
        method: 'POST',
        data: data,
      });
      if (resp?.status) {
        if (type === 'job') {
          setJobDetail({
            ...jobDetail,
            isSaved: resp?.data === 'unsaved' ? false : true,
          });
        } else {
          setJobDetail((prev: any) => ({
            ...prev,
            approvedApplicant: {
              ...prev.approvedApplicant,
              savedUser: resp?.data === 'created' ? true : false, // or false depending on your logic
            },
          }));
        }
        setSaveLoader('');
      } else {
        setSaveLoader('');
      }
      setSaveLoader('');
    } catch (e) {
      setSaveLoader('');
      console.log('ERRR', e);
    }
  };

  const isJobValidUser = (jobDetail?.approvedApplicant?.userId === userData?.id || jobDetail?.userId === userData?.id);

  return (
    <View style={[styles.container]}>
      <AnimatedView>
        <Header
          leftIcon="back-arrow"
          title={capitalizeFirstLetterOfEachWord(jobDetail?.title)}
          rightIcons={
            isEmployer &&
            (jobDetail?.approvedApplicant?.status === 'Approved' ||
              jobDetail?.approvedApplicant?.status === 'Completed')
              ? null
              : true
                ? [
                  {
                    icon: 'ButtonMd',
                    onPress: showMenu,
                    // wrapStyle: styles.msgIconStyle,
                    wrapStyle: BaseStyles.notificationIconStyle,
                  },
                ]
                : null
          }
          onLeftPress={() => {
            if (params?.canGoBack === false) {
              resetStack();
            } else {
              navigation.goBack();
            }
          }}
        />
        {!isEmpty(jobDetail) || listLoader ? (
          <>
            <View style={{ position: 'absolute', right: 20, top: IOS ? 90 : 40 }}>
              <Menu visible={visible} onRequestClose={hideMenu}>
                {isEmployer ? (
                  <>
                    {jobDetail?.approvedApplicant?.status === 'Approved' ||
                    jobDetail?.approvedApplicant?.status ===
                      'Completed' ? null : (
                        <>
                          <MenuItem
                            textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                            onPress={clickEdit}>
                            {translate('editListing', '')}
                          </MenuItem>
                          <MenuItem
                            textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                            onPress={clickDelete}>
                            {translate('cancelListing', '')}
                          </MenuItem>
                          {isEmployer === true ? null : (
                            <>
                              <MenuItem
                                textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                                onPress={openEmployerProfile}>
                                {translate('viewEmployerProfile', '')}
                              </MenuItem>
                              <MenuItem
                                textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                                onPress={openChat}>
                                {translate('message', '')}
                              </MenuItem>
                              <MenuItem
                                onPress={openReport}
                                textStyle={{ fontFamily: FontFamily.OpenSansBold }}>
                                {translate('contactUs', '')}
                              </MenuItem>
                            </>
                          )}
                        </>
                      )}
                    {(jobDetail?.isApplied?.status === 'Completed' ||
                      jobDetail?.approvedApplicant?.status === 'Completed') &&
                      !jobDetail?.isReviewed && (
                      <>
                        {/* <MenuItem
                            textStyle={{fontFamily: FontFamily.OpenSansBold}}
                            onPress={clickReview}>
                            {translate('addReview', '')}
                          </MenuItem> */}
                        {isEmployer === true ? null : (
                          <>
                            <MenuItem
                              textStyle={{
                                fontFamily: FontFamily.OpenSansBold,
                              }}
                              onPress={openEmployerProfile}>
                              {translate('viewEmployerProfile', '')}
                            </MenuItem>
                            {/* <MenuItem
                                textStyle={{                                   fontFamily: FontFamily.OpenSansBold                                 }}
                                onPress={openChat}
>
                                {translate('message', '')}
                              </MenuItem> */}
                            <MenuItem
                              textStyle={{
                                fontFamily: FontFamily.OpenSansBold,
                              }}
                              onPress={openReport}>
                              {translate('contactUs', '')}
                            </MenuItem>
                          </>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  <>
                    {jobDetail?.isApplied?.status === 'Completed' &&
                      !jobDetail?.isReviewed && (
                      <></>
                    // <MenuItem
                    // textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                    //   onPress={clickReview}>
                    //   {translate('addReview', '')}
                    // </MenuItem>
                    )}
                    {jobStatus === 'pending' ||
                    jobDetail?.isApplied?.status === 'Approved' ||
                    jobDetail?.isApplied?.status === 'Completed' ||
                    !jobDetail?.isApplied ? null : (
                        <MenuItem
                          textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                          onPress={() => {
                            setVisible(false);
                            setOpenDeleteModal(false);
                            setOpenLogoutBottomSheet(true);
                            setTimeout(() => {
                              refRBSheet?.current?.open?.();
                            }, 1000);
                          }}>
                          {translate('cancelApplication')}
                        </MenuItem>
                      )}

                    {isEmployer === true ? null : (
                      <>
                        <MenuItem
                          textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                          onPress={openChat}>
                          {translate('message', '')}
                        </MenuItem>
                        <MenuItem
                          onPress={openReport}
                          textStyle={{ fontFamily: FontFamily.OpenSansBold }}>
                          {translate('contactUs', '')}
                        </MenuItem>
                        <MenuItem
                          textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                          onPress={async () => {
                            try {
                              await Share.share({
                                message: `${translate('checkThisOut')}${BaseSetting.webUrl}job-details/${jobDetail?.id}`,
                              });
                            } catch (error) {
                              console.error('Error sharing link', error);
                            }
                          }}>
                          {translate('shareJob', '')}
                        </MenuItem>
                        <MenuItem
                          textStyle={{ fontFamily: FontFamily.OpenSansBold }}
                          onPress={openEmployerProfile}>
                          {translate('viewEmployerProfile', '')}
                        </MenuItem>
                      </>
                    )}
                  </>
                )}
              </Menu>
            </View>
            {listLoader ? (
              <View style={styles.rowCenter}>
                <ActivityIndicator size="small" color={BaseColors.primary} />
              </View>
            ) : (
              <>
                <ScrollView
                  nestedScrollEnabled={true}
                  contentContainerStyle={{ flexGrow: 1 }}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={onRefresh}
                      colors={[BaseColors.primary]} // Customize refresh indicator color
                      tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
                    />
                  }>
                  <AnimatedView>
                    <View style={styles.container}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: approvedStatus
                            ? 'space-between'
                            : 'center',
                          marginHorizontal: 15,
                          alignItems: 'center',
                        }}>
                        {approvedStatus ? (
                          <View style={styles.skillsView}>
                            <Text style={styles.skillTxtSty}>
                              {translate('jobid', '')} :{' '}
                              {jobDetail?.referenceNumber}
                            </Text>
                          </View>
                        ) : null}
                        <View style={{ flexDirection: 'row' }}>
                          <Text
                            style={{ fontFamily: FontFamily.OpenSansRegular }}>
                            {timeAgo}
                          </Text>
                          {isEmpty(jobDetail?.approvedApplicant) &&
                          jobDetail?.isApplied?.status !== 'Approved' ? (
                              <TouchableOpacity
                                onPress={() => {
                                  if (
                                    jobDetail?.userId === userData?.id &&
                                  jobDetail?.applicantsCount > 0
                                  ) {
                                    navigation.navigate('Applicants', {
                                      job: jobDetail,
                                      Id: jobDetail?.id,
                                    });
                                  }
                                }}
                                activeOpacity={1}
                                style={{ paddingHorizontal: 5 }}>
                                <Text
                                  style={{
                                    textDecorationLine:
                                    jobDetail?.userId === userData?.id &&
                                    jobDetail?.applicantsCount > 0
                                      ? 'underline'
                                      : 'none',
                                    color: BaseColors.primary,
                                    fontFamily: FontFamily.OpenSansRegular,
                                  }}>
                                  {jobDetail?.applicantsCount}{' '}
                                  {translate('Apllicants', '')}
                                </Text>
                              </TouchableOpacity>
                            ) : null}
                        </View>
                      </View>
                      <View style={styles.row}>
                        {!isEmpty(jobDetail?.jobData) &&
                        isArray(jobDetail?.jobData) &&
                        jobDetail?.jobData
                          ? jobDetail?.jobData?.map((skill, index) => (
                            <View style={styles.skillView}>
                              <Text key={index} style={styles.skillText}>
                                {skill?.skillData?.name}
                              </Text>
                            </View>
                          ))
                          : null}
                      </View>
                      <View style={{ paddingHorizontal: 15, marginTop: 5 }}>
                        <EmployarCard
                          item={jobDetail}
                          onSave={() => {
                            handleSave('job');
                          }}
                          saveLoader={saveLoader}
                          navigation={navigation}
                          navigateName={'ApplicantDetails'}
                          type="viewEmployer"
                        />
                        {userData?.id !== jobDetail?.userId ? null : isEmpty(
                          jobDetail?.approvedApplicant,
                        ) ? null : (
                            <SeekerCard
                              item={jobDetail}
                              onSave={() => {
                                handleSave('user');
                              }}
                              saveLoader={saveLoader}
                              applicantsLoader={null}
                              navigation={navigation}
                              applicantType={'applicantType'}
                              Id={jobDetailId}
                              list={jobDetail}
                            />
                          )}

                        {!isEmpty(counterHistory) &&
                          !isEmployer &&
                          status !== 'declined' && (
                          <CounterOfferCard
                            item={counterHistory[0]}
                            saveLoader={false}
                            type={'applicants'}
                            onActionClick={handleClick}
                            handleUpdateJob={handleRefresh}
                            applicantsLoader={false}
                            showApplicant={true}
                            navigation={navigation}
                            jobDetail={{ ...jobDetail, paymentProcessing: paymentInProcess ? true : false }}
                            buttons
                            Id={jobDetail?.id}
                          />
                        )}
                      </View>

                      {jobDetail?.isApplied?.status === 'Pending' &&
                      jobDetail?.type === 'custom' ? null : (
                          <>
                            {(userData?.id === jobDetail?.userId &&
                            jobDetail?.approvedApplicant?.status) ||
                          (userData?.id !== jobDetail?.userId &&
                            jobDetail?.isApplied?.status) ? (
                                <View style={{ marginBottom: 0 }}>
                                  <StepIndicator
                                    activeStep={activeStep}
                                    steps={steps}
                                    goToStep={goToStep}
                                    IOS={Platform.OS === 'ios'}
                                    item={jobDetail}
                                  />
                                </View>
                              ) : null}
                          </>
                        )}
                      {/* {list?.isApplied ? (
              <View style={{marginBottom: 20}}>
                <StepIndicator
                  activeStep={activeStep}
                  steps={steps}
                  goToStep={goToStep}
                  IOS={Platform.OS === 'ios'}
                  item={list}
                />
              </View>
            ) : null} */}
                      {isEmpty(jobDetail?.approvedApplicant) ? (
                        <>
                          {userData?.id === jobDetail?.userId &&
                          !isEmpty(jobapplicantList) &&
                          jobapplicantList?.length !== 0 ? (
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  paddingHorizontal: 15,
                                }}>
                                <Text
                                  style={{
                                    color: '#202020',
                                    fontFamily: FontFamily.OpenSansBold,
                                    fontSize: 14,
                                  }}>
                                  {translate('Applicants', '')}
                                </Text>
                                {!isEmpty(jobapplicantList) &&
                              jobapplicantList?.length === 0 ? null : (
                                    <TouchableOpacity
                                      onPress={() => {
                                        navigation.navigate('Applicants', {
                                          job: jobDetail,
                                          Id: jobDetail?.id,
                                        });
                                      }}>
                                      <Text
                                        style={{
                                          color: BaseColors.primary,
                                          fontFamily: FontFamily.OpenSansRegular,
                                          fontSize: 14,
                                          textTransform: 'capitalize',
                                        }}>
                                        {translate('Seeall', '')}
                                      </Text>
                                    </TouchableOpacity>
                                  )}
                              </View>
                            ) : null}
                          {!isEmpty(jobapplicantList) ? (
                            <FlatList
                              horizontal
                              data={jobapplicantList}
                              renderItem={renderApplicantCard}
                              keyExtractor={(_, index) => index.toString()}
                              showsHorizontalScrollIndicator={false}
                              contentContainerStyle={styles.applicantsList}
                              pagingEnabled
                              scrollEventThrottle={16} // Ensures smoother scroll event handling
                              snapToAlignment="start"
                              snapToInterval={width / 1.1}
                              decelerationRate="fast"
                              onScroll={handleScroll}
                              nestedScrollEnabled={true}
                              keyboardShouldPersistTaps="handled"
                            />
                          ) : null}
                          {/* Pagination */}
                          {!isEmpty(groupedApplicants) ? (
                            <View style={styles.pagination}>
                              {groupedApplicants?.map((_, index) => (
                                <View
                                  key={index}
                                  style={[
                                    styles.paginationDot,
                                    activeIndex === index && styles.activeDot,
                                  ]}
                                />
                              ))}
                            </View>
                          ) : null}
                        </>
                      ) : null}
                      {jobDetail?.images?.length > 0 && (
                        <View
                          style={{
                            paddingHorizontal: 15,
                            paddingBottom: 10,
                            paddingTop: isEmpty(jobDetail?.approvedApplicant)
                              ? 0
                              : 15,
                          }}>
                          <Text style={styles.sectionTitle}>
                            {translate('Images', '')}
                          </Text>
                          <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={styles.imagesRow}>
                            {jobDetail?.images?.map(
                              (image: any, index: number) => {
                                console.log('🚀 ~ image:', image);
                                return (
                                  <TouchableOpacity
                                    style={styles.imageWrapper}
                                    key={index}
                                    onPress={() => {
                                      if (
                                        imagePattern.test(
                                          image || jobDetail?.images,
                                        )
                                      ) {
                                        // Filter out non-image files from the array
                                        const imageFiles =
                                          jobDetail?.images?.filter(
                                            (file: string) =>
                                              imagePattern.test(file),
                                          ) || [];
                                        navigation.navigate('GalleryView', {
                                          images: imageFiles,
                                          index: index,
                                        });
                                      } else if (
                                        /\.(pdf|docx)$/i.test(
                                          image || jobDetail?.images,
                                        )
                                      ) {
                                        navigation.navigate('WebViewScreen', {
                                          uri: image || jobDetail?.images,
                                          type: 'jobView',
                                        });
                                      }
                                      // Handle other file types if needed (e.g., show file icon)
                                      else {
                                        console.log(
                                          'File type not supported for preview',
                                        );
                                      }
                                      // navigation.navigate('GalleryView', {
                                      //   images: jobDetail?.images,
                                      //   index,
                                      // });
                                    }}>
                                    {image || jobDetail?.images ? (
                                      // Check for image files
                                      /\.(png|jpg|jpeg|gif|webp)$/i.test(
                                        image || jobDetail?.images,
                                      ) ? (
                                          <FastImage
                                            source={{ uri: image }}
                                            style={styles.uploadedImage}
                                            resizeMode="cover"
                                          />
                                        ) : /\.(pdf|docx)$/i.test(
                                          image || jobDetail?.images,
                                        ) ? (
                                        // Check for document files (pdf, docx, etc.)
                                            <View
                                              style={[
                                                styles.uploadedImage,
                                                {
                                                  alignItems: 'center',
                                                  justifyContent: 'center',
                                                  borderWidth: 1,
                                                  borderColor:
                                                BaseColors.borderColor,
                                                },
                                              ]}>
                                              <FIcon
                                                name="file-pdf-o" // You can replace this with the appropriate icon if needed
                                                size={30}
                                                color={BaseColors.primary}
                                              />
                                            </View>
                                          ) : (
                                        // Default for unsupported file types
                                            <FIcon
                                              name="file"
                                              size={30}
                                              color={BaseColors.primary}
                                            />
                                          )
                                    ) : (
                                      <FIcon
                                        name="file"
                                        size={30}
                                        color={BaseColors.primary}
                                      />
                                    )}
                                    {/* <FastImage
                                      source={{uri: image}}
                                      style={styles.uploadedImage}
                                      resizeMode="cover"
                                    /> */}
                                  </TouchableOpacity>
                                );
                              },
                            )}
                          </ScrollView>
                        </View>
                      )}
                      {isEmpty(jobDetail?.description) ? null : (
                        <View style={styles.section}>
                          <Text style={styles.sectionTitle}>
                            {translate('Description', '')}
                          </Text>
                          <Text style={styles.descriptionText}>
                            {capitalizeFirstLetter(jobDetail?.description)}
                          </Text>
                        </View>
                      )}
                      <View style={{ marginHorizontal: 20 }}>
                        <PaymentBreakdown
                          type={isEmployer ? 'employer' : 'seeker'}
                          totalSalary={jobDetail?.totalSalaryAmount || '-'}
                          serviceChargePer={
                            isEmployer
                              ? jobDetail?.employerServiceChargePer
                              : jobDetail?.seekerServiceChargePer
                          }
                          serviceCharge={
                            isEmployer
                              ? `${jobDetail?.serviceCharge || 0}`
                              : `${jobDetail?.seekerServiceCharge}` || 0
                          }
                          totalWithServiceCharge={
                            isEmployer
                              ? jobDetail?.totalEstimateCharge || 0
                              : jobDetail?.seekerPayableAmount || 0
                          }
                        />
                      </View>

                      {jobDetail?.isPriceOptional &&
                        !jobDetail?.isDirectApplied &&
                        jobDetail?.isApplied?.status !== 'Declined' &&
                        (status == 'approved' ||
                          status == 'completed' ||
                          status == 'declined') && (
                        <>
                          <View style={[styles.marginBt]}>
                            <Text style={styles?.seekerSty}>
                              {translate('counterHistory', '')}
                            </Text>

                            <TouchableOpacity
                              onPress={() => {
                                navigation.navigate('CounterHistory', {
                                  item: jobDetail?.approvedApplicant,
                                  job: jobDetail,
                                });
                              }}>
                              <Text
                                style={{
                                  color: BaseColors.primary,
                                  fontFamily: FontFamily.OpenSansRegular,
                                  fontSize: 14,
                                  textTransform: 'capitalize',
                                }}>
                                {translate('Seeall', '')}
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <CounterHistory
                            jobDetail={jobDetail}
                            applicant={jobDetail?.approvedApplicant}
                          />
                        </>
                      )}

                      <View style={{ marginHorizontal: 20 }}>
                        {jobDetail?.reviews?.length > 0 && (
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              marginTop: 15,
                              marginBottom: 5,
                              alignItems: 'center',
                            }}>
                            <View>
                              <Text style={styles?.seekerSty}>
                                {jobDetail?.userId === userData?.id
                                  ? translate('seekerReview', '')
                                  : translate('employerReview', '')}
                              </Text>
                            </View>

                            <TouchableOpacity
                              onPress={() => {
                                const role =
                                  userData?.id === jobDetail?.userId
                                    ? 'Employer'
                                    : 'Seeker';
                                navigation.navigate('ReviewList', {
                                  id:
                                    role === 'Seeker'
                                      ? jobDetail.userId
                                      : jobDetail?.approvedApplicant?.userId,
                                  type: role,
                                });
                              }}>
                              <Text style={styles?.seeMoreTxt}>
                                {translate('seeMore', '')}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        )}
                        {jobDetail?.reviews?.map(
                          (review: any, index: number) => {
                            const reviewId = review.id || index;
                            const isExpanded = expandedReviews[reviewId];
                            const showSeeMore = isTruncated[reviewId];

                            return (
                              <ReviewRatingComponant
                                review={review}
                                reviewId={reviewId}
                                isExpanded={isExpanded}
                                showSeeMore={showSeeMore}
                                toggleExpansion={toggleExpansion}
                                handleLayout={handleLayout}
                                getTimeAgo={getTimeAgo}
                                type=""
                              />
                            );
                          },
                        )}
                      </View>
                    </View>
                  </AnimatedView>
                </ScrollView>
                {/* {userData?.id !== jobDetail?.userId &&
              (jobDetail?.isApplied === null ||
                jobDetail?.isApplied?.status === 'Pending') ? (
                <>
                  <View style={styles.checkboxView}>
                    <TouchableOpacity
                      activeOpacity={1}
                      onPress={() => {
                        setClick(!click);
                        setCheckBoxErr(false);
                      }}
                      style={styles.clickSty}>
                      {click ? (
                        <CustomIcon
                          name="checked"
                          style={{ color: BaseColors.primary }}
                          size={15}
                        />
                      ) : null}
                    </TouchableOpacity>
                    <View style={styles.mView}>
                      <View style={styles.txtaggrementView}>
                        <Text style={{ color: BaseColors.textColor }}>
                        &nbsp; I agree to Harbor's {''}
                        </Text>
                        <TouchableOpacity
                          activeOpacity={0.7}
                          onPress={() => {
                            navigation.navigate('Policy', {
                              type: 'terms_of_service',
                              title: 'terms and services',
                            });
                          }}>
                          <Text style={styles.txtnavigationSty}>
                          terms and services,
                          </Text>
                        </TouchableOpacity>
                        <Text>{''} </Text>
                        <TouchableOpacity
                          activeOpacity={0.7}
                          onPress={() => navigation.navigate('PrivacyPolicy')}>
                          <Text style={styles.txtnavigationSty}>
                            {' '}
                          privacy policy,
                          </Text>
                        </TouchableOpacity>
                        <Text> </Text>
                        <TouchableOpacity
                          activeOpacity={0.7}
                          onPress={() => {
                            navigation.navigate('Policy', {
                              type: 'cancellation_policy',
                              title: 'cancellation policy',
                            });
                          }}>
                          <Text style={styles.txtnavigationSty}>
                          cancellation policy.
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  <View style={styles.errView}>
                    {checkBoxErr && (
                      <Text style={styles.checkboxErr}>{checkBoxErrTxt}</Text>
                    )}
                  </View>
                </>
              ) : null} */}

                {/* {paymentInProcess && (
                  <View
                    style={{
                      ...styles.declinedStatus,
                      borderColor: BaseColors.borderColor,
                    }}>
                    <Text
                      style={{
                        ...styles.declineText,
                        color: BaseColors.textGrey,
                      }}>
                      {translate('paymentProcessing')}
                    </Text>
                  </View>
                )} */}
                {sendCustomOfferView && !listLoader  && jobDetail?.status !== 'completed' && (
                  <View
                    style={{
                      marginTop: 20,
                      marginHorizontal: 15,
                    }}>
                    <Button
                      onPress={() => {
                        if (userData?.isProfileSet === false) {
                          Toast.show(translate('addProfileDetail', ''));
                          return false;
                        } else {
                          setState({
                            ...state,
                            customOfferModal: true,
                            applicant: jobDetail,
                          });
                        }
                      }}
                      type="text">
                      {translate('customOffer')}
                    </Button>
                  </View>
                )}

                {(jobDetail?.isApplied?.status !== 'Approved' ||
                  jobDetail?.isApplied?.status !== 'Declined') &&
                jobDetail?.isApplied?.status === 'Pending' &&
                isCustomHarbor &&
                !listLoader ? (
                    <View style={{ marginHorizontal: 15 }}>
                      {!isEmpty(jobDetail?.approvedApplicant) ||
                    jobDetail?.isApplied?.status ? (
                          <View style={{ marginTop: 20 }}>
                            <Button
                              loading={loader === 'chat'}
                              onPress={handleChatNavigation}
                              type="outlined">
                              {translate('message', '')}
                            </Button>
                          </View>
                        ) : null}
                    </View>
                  ) : null}
                {jobDetail?.isApplied?.status === 'Approved' ||
                jobDetail?.isApplied?.status === 'Declined' ||
                listLoader ? null : (
                    <>
                      {userData?.id !== jobDetail?.userId &&
                    (jobDetail?.isApplied === null ||
                      jobDetail?.isApplied?.status === 'Pending') ? (
                          <View
                            style={{
                              flexDirection:
                            jobDetail?.isApplied?.status === 'Pending' &&
                            isCustomHarbor
                              ? 'row'
                              : null,
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              marginHorizontal: 15,
                            }}>
                            {rejectBtnView && !sendCustomOfferView ? (
                              <View style={{ width: '48%', marginVertical: 20 }}>
                                <Button
                                  loading={loader === 'reject'}
                                  onPress={() => {
                                    handleCancelPost('reject');
                                  }}
                                  type="outlined">
                                  {translate('reject', '')}
                                </Button>
                              </View>
                            ) : null}
                            {acceptApplyView ? (
                              <View
                                style={{
                                  marginVertical: 20,
                                  width:
                                jobDetail?.isApplied?.status === 'Pending' &&
                                isCustomHarbor
                                  ? sendCustomOfferView
                                    ? '85%'
                                    : '48%'
                                  : '100%',
                                }}>
                                <Button
                                  loading={loader === true}
                                  onPress={validation}
                                  style={
                                    isCustomHarbor && sendCustomOfferView
                                      ? { ...styles.approveBtn }
                                      : null
                                  }
                                  type={
                                    isCustomOffer &&
                                isNotApplied &&
                                isSeeker &&
                                !isCustomHarbor
                                      ? 'outlined'
                                      : 'text'
                                  }>
                                  {translate(
                                    isCustomHarbor
                                      ? sendCustomOfferView
                                        ? 'Approve'
                                        : 'accept'
                                      : 'apply',
                                    '',
                                  )}
                                </Button>
                              </View>
                            ) : null}
                            {rejectBtnView && sendCustomOfferView && (
                              <TouchableOpacity
                                style={{
                                  ...styles.crossMainView,
                                  borderColor: BaseColors.red,
                                }}
                                onPress={() => {
                                  handleCancelPost('reject');
                                }}>
                                {false ? (
                                  <ActivityIndicator color={BaseColors.red} />
                                ) : (
                                  <Entypo
                                    name={'cross'}
                                    size={24}
                                    color={BaseColors.red}
                                  />
                                )}
                              </TouchableOpacity>
                            )}
                          </View>
                        ) : null}
                    </>
                  )}
              </>
            )}

            {declinedView ? (
              <View style={styles.declinedStatus}>
                <Text style={styles.declineText}>
                  {translate(employerReject ? 'empDeclined' : 'userDeclined')}
                </Text>
              </View>
            ) : null}

            {jobDetail?.isApplied?.status === 'Pending' &&
            isCustomHarbor ? null : (
                <>
                  {!isEmpty(jobDetail?.approvedApplicant) ||
                jobDetail?.isApplied?.status ? (
                      <>
                        {(jobDetail?.isApplied?.status === 'Completed' ||
                      jobDetail?.approvedApplicant?.status === 'Completed') &&
                    !jobDetail?.isReviewed ? null : (
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                marginHorizontal: 15,
                                marginTop: 20,
                              }}>
                              <View
                                style={{
                                  width:
                              (jobDetail?.isApplied?.status === 'Completed' ||
                                jobDetail?.approvedApplicant?.status ===
                                  'Completed') &&
                              !jobDetail?.isReviewed
                                ? '46%'
                                : '100%',
                                }}>
                                <Button
                                  loading={loader === 'chat'}
                                  onPress={handleChatNavigation}
                                  type={
                                    jobDetail?.status === 'completed' &&
                              jobDetail?.approvedApplicant &&
                              isEmployer &&
                              jobDetail?.isReviewed
                                      ? 'outlined'
                                      : 'text'
                                  }>
                                  {translate('message', '')}
                                </Button>
                              </View>
                            </View>
                          )}
                      </>
                    ) : null}
                </>
              )}

            {/* {jobDetail?.status === 'completed' &&
            jobDetail?.approvedApplicant &&
            isEmployer ? (
                <View style={{ marginHorizontal: 15, marginVertical: 10 }}>
                  <Button
                    onPress={() => {
                      navigation.navigate('JobPosting', {
                        type: 'custom',
                        applicantId:
                        jobDetail?.approvedApplicant?.seekerId ||
                        jobDetail?.approvedApplicant?.userId,
                        roomId: jobDetail?.approvedApplicant?.roomId,
                      });
                    }}
                    type="text">
                    {translate('hireAgain', '')}
                  </Button>
                </View>
              ) : null} */}
            {(jobDetail?.isApplied?.status === 'Completed' ||
              jobDetail?.approvedApplicant?.status === 'Completed') &&
              !jobDetail?.isReviewed && isJobValidUser && (
              <View
                style={{
                  marginHorizontal: 15,
                  marginTop: 10,
                  gap: 10,
                }}>
                <View>
                  <Button onPress={clickReview} type="text" loading={false}>
                    {translate('addReview', '')}
                  </Button>
                </View>

                <Button
                  loading={loader === 'chat'}
                  onPress={handleChatNavigation}
                  type={'outlined'}>
                  {translate('message', '')}
                </Button>
              </View>
            )}
            {cAlert.showAlert && (
              <AlertModal
                prompt
                image
                title={translate('allsystemGo', '')}
                visible={cAlert.showAlert}
                setVisible={val =>
                  setCAlert(prevState => ({ ...prevState, showAlert: val }))
                }
                description={cAlert.message || ''}
                btnOkPress={() => {
                  setCAlert(prevState => ({ ...prevState, showAlert: false }));
                  getList(); // Call getList to refresh the data after closing the modal
                }}
              />
            )}
            <LanguageModal
              loader={
                openDeleteModal ? openDeleteModalLoader : cancellationLoader
              }
              refRBSheet={refRBSheet}
              setOpenBottomSheet={setOpenLogoutBottomSheet}
              title={translate('cancelApplication', '')}
              openLanguage={false}
              cancelProp={translate('Cancel', '')}
              onClick={openDeleteModal ? deleteFile : handleCancelPost}
              doneProp={translate('cancleBtnApplication', '')}
              showDescription={true}
              Description={translate('cancelDiscription', '')}
              deleteSty={openBottomSheet ? false : true}
              showCancelbutton={true}
            />
          </>
        ) : (
          <View
            style={[
              styles.centerMain,
              // { marginTop: Dimensions.get('screen').height / 3  }
            ]}>
            <NoRecord
              title={'noJobsFound'}
              description={'noJobsFoundDesc'}
              type="noJobsFound"
            />
          </View>
        )}
        <CustomOfferModal
          setState={setState}
          state={state}
          title={translate('customOffer')}
          jobDetail={jobDetail}
          onSubmit={(values: any) => {
            // getList();
            onSubmit(values, 'custom');
          }}
          navigation={navigation}
          onCancel={handleOnClose}
        />

        {state.confirmationModal && (
          <AlertModal
            image
            title={translate('declineJob', '')}
            visible={state.confirmationModal}
            setVisible={(val: any) =>
              setState((p: any) => ({ ...p, confirmationModal: false }))
            }
            btnYPress={async () => {
              if (state?.applicant?.counterOfferId) {
                onSubmit({});
              } else {
                setState((p: any) => ({ ...p, applicantsLoader: true }));
                const resp = await updateJobStatus('Declined', {
                  jobId: jobDetail?.id,
                  userId: state?.applicant?.id,
                });
                if (resp?.status) {
                  setState((p: any) => ({
                    ...p,
                    confirmationModal: false,
                    applicant: {},
                    job: {},
                  }));
                  getList();
                }
              }
            }}
            loader={state?.applicantsLoader}
            btnYTitle={translate('YES')}
            btnNTitle={translate('CANCEL')}
            btnNPress={() => {
              setState((p: any) => ({ ...p, confirmationModal: false }));
            }}
            confirmation
          />
        )}

        <AlertModal
          image
          title={translate('complete', '')}
          visible={modalOpen.confirmationModal}
          setVisible={(val: any) =>
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
          }
          lottieViewVisible
          btnYPress={() => {
            navigation.navigate('ProfileSetUp');
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
          loader={modalOpen?.loader}
          btnYTitle={translate('letsdo')}
          confirmation
          // completeProfile
          titlesty={{ textAlign: 'center' }}
          description={translate('postDescription', '')}
          btnNTitle={translate('maybe')}
          btnNPress={() => {
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
        />
      </AnimatedView>
    </View>
  );
}
