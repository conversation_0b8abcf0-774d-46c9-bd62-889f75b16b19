import { BaseColors } from '@config/theme';
import React from 'react';
import { TouchableOpacity, StyleSheet, View, ViewStyle, Dimensions } from 'react-native';
import Entypo from 'react-native-vector-icons/Entypo';
import Micon from 'react-native-vector-icons/MaterialCommunityIcons';

const { width, height } = Dimensions.get('window');

type FloatingButtonProps = {
  onPress: () => void;
  iconName?: string;
  iconColor?: string;
  backgroundColor?: string;
  style?: ViewStyle;
};

const FloatingButton: React.FC<FloatingButtonProps> = ({
  onPress,
  iconName = 'add',
  iconColor = '#fff',
  backgroundColor = BaseColors.primary,
  style,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.floatingButton, { backgroundColor }, style]}
        onPress={onPress}
        activeOpacity={0.7}>
        {iconName === 'fire' ? (
          <Micon name={iconName} color={iconColor} size={20} />
        ) : (
          <Entypo name={iconName} color={iconColor} size={20} />
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: width * 0.02, // 2% from the right
    bottom: height * 0.07, // roughly 55 on standard 812 height screen
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  floatingButton: {
    width: width * 0.1, // approx 40 on a 400px wide screen
    height: width * 0.1,
    borderRadius: (width * 0.1) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: height * 0.0025 },
    shadowOpacity: 0.3,
    shadowRadius: width * 0.01,
    elevation: 5,
  },
  streakTrackerContainer: {
    marginRight: width * 0.025, // approx 10 on 400px screen
  },
});

export default FloatingButton;
