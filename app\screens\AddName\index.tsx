import React, {useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  Keyboard,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import FastImage from 'react-native-fast-image';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Images} from '../../config/images';
import {translate} from '@language/Translate';
import TextInput from '../../components/UI/TextInput';
import Button from '@components/UI/Button';
import styles from './styles';
import Toast from 'react-native-simple-toast';
import AuthAuthentication from '../../redux/reducers/auth/actions';
import {getApiData} from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import {isEmpty} from '@app/utils/lodashFactions';
import {BaseColors} from '@config/theme';
import {CustomIcon} from '@config/LoadIcons';
import LottieView from 'lottie-react-native';
import {BackHandler} from 'react-native';
import { resetStack } from '@app/utils/CommonFunction';

interface Props {
  navigation: any; // Replace `any` with the specific navigation type if possible
  route: any; // Replace `any` with the specific route type if available
}

// Define the ErrorState type
interface ErrorState {
  err: boolean;
  txt: string;
}

const AdName: React.FC<Props> = ({navigation}) => {
  let backPressed = 0;
  const {userProfileData, userData} = useSelector((state: any) => state.auth); // Use your RootState type
  const dispatch = useDispatch();
  console.log('userProfileData', userProfileData);
  console.log('userData ===>', userData);
  const {setUserProfileData, setUserData} = AuthAuthentication;
  const [click, setClick] = useState<boolean>(false);
  const [checkBoxErr, setCheckBoxErr] = useState(false);
  const [checkBoxErrTxt, setCheckBoxErrTxt] = useState('');
  const nameRef = useRef<any>(null);
  const lastNameRef = useRef<any>(null);

  const [name, setName] = useState<string>(
    userProfileData?.firstName || userData?.firstName || '',
  );
  const [lastName, setLastName] = useState<string>(
    userProfileData?.lastName || userData?.lastName || '',
  );
  const [nameError, setNameError] = useState<ErrorState>({err: false, txt: ''});
  const [lastNameError, setLastNameError] = useState<ErrorState>({
    err: false,
    txt: '',
  });

  const [loader, setLoader] = useState(false);
  const IOS = Platform.OS === 'ios';

  // Validation function
  const validateField = (value: string, fieldName: string): string | null => {
    const regex = /^[a-zA-Z]{2,25}$/; // Allows only letters, 2-25 characters
    if (!value) {
      return `${fieldName} is required.`;
    }
    if (!regex.test(value)) {
      return `Only letters are allowed in ${fieldName}, 2-25 characters.`;
    }
    return null;
  };

  const handleSubmit = async () => {
    setLoader(true);
    const newObj: any = {
      firstName: name || undefined,
      lastName: lastName || undefined,
    };

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateUser,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        const d = !isEmpty(res?.data) ? res?.data : {};
        dispatch(
          setUserProfileData({
            ...userProfileData, // Retain existing userProfileData properties
            ...d,
            firstName: name, // Update the firstName
            lastName: lastName, // Update the lastName
          }) as any,
        );
        dispatch(setUserData({ ...userData, isProfileSetupOngoing: false }) as any);
        // navigation.navigate('ProfileSetUp', {
        //   redirection: 'BottomtabsNavigator',
        // });
        resetStack('BottomTabsNavigator');
      } else {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        console.log('error', res);
      }
      setLoader(false);
    } catch (err) {
      console.log('check Error');
      setLoader(false);
      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const handleContinue = () => {
    // Validate name and lastName with specific error messages
    const firstNameError = validateField(name, 'First Name');
    const lastNameErrorText = validateField(lastName, 'Last Name');

    // Update error state based on validation
    setNameError(
      firstNameError ? {err: true, txt: firstNameError} : {err: false, txt: ''},
    );
    setLastNameError(
      lastNameErrorText
        ? {err: true, txt: lastNameErrorText}
        : {err: false, txt: ''},
    );

    // If no errors, proceed
    if (!firstNameError && !lastNameErrorText) {
      if (!click) {
        setCheckBoxErr(true);
        setCheckBoxErrTxt('Please check the agreement before proceeding.');
        return;
      } else {
        handleSubmit();
      }
    }
  };

  // this is for hard back from app....
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      // Toast.show("Press again to exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        {/* <StatusBar barStyle="dark-content" backgroundColor={BaseColors.white} /> */}
        <View style={styles.mainViewSty}>
          <View>
            <View style={styles.imageContainer}>
              <LottieView
                resizeMode="contain"
                source={Images.introscreenTwo}
                autoPlay={true}
                loop={true}
                style={{
                  width: Dimensions.get('screen').width,
                  height: IOS
                    ? Dimensions.get('screen').width - 140
                    : Dimensions.get('screen').width - 150,
                }}
              />
            </View>
            <View style={styles.titleContainerSty}>
              <Text style={styles.headingText}>
                {translate('welcomeAbroad', '')}
              </Text>
            </View>
            <Text style={styles.bottomText}>
              {translate('letsshowsucess', '')}
            </Text>
            <View style={styles.lNameViewSty}>
              <TextInput
                ref={nameRef}
                title={''}
                placeholderText={translate('firstName', '')}
                returnKeyType="next"
                value={name}
                mandatory={true}
                onChange={(value: string) => {
                  setName(value);
                  setNameError({err: false, txt: ''}); // Clear error on change
                }}
                showError={nameError.err} // Boolean for error visibility
                errorText={nameError.txt} // Error message
                maxLength={25}
                onSubmit={() => {
                  lastNameRef.current?.focus();
                }}
              />
            </View>
            <View style={styles.lNameViewSty}>
              <TextInput
                ref={lastNameRef}
                title={''}
                placeholderText={translate('lastName', '')}
                value={lastName}
                returnKeyType="done"
                mandatory={true}
                onChange={(value: string) => {
                  setLastName(value);
                  setLastNameError({err: false, txt: ''}); // Clear error on change
                }}
                showError={lastNameError.err} // Boolean for error visibility
                errorText={lastNameError.txt} // Error message
                maxLength={25}
                onSubmit={Keyboard.dismiss}
              />
            </View>
          </View>
        </View>
        <View style={styles.checkboxView}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              setClick(!click);
              setCheckBoxErr(false);
            }}
            style={styles.clickSty}>
            {click ? (
              <CustomIcon
                name="checked"
                style={{color: BaseColors.primary}}
                size={15}
              />
            ) : null}
          </TouchableOpacity>
          <View style={styles.mView}>
            <View style={styles.txtaggrementView}>
              <Text style={{color: BaseColors.textColor}}>
                &nbsp; I agree to Harbor's {''}
              </Text>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  navigation.navigate('WebViewScreen', {
                    type: 'terms',
                    title: translate('terms', ''),
                  });
                }}>
                <Text style={styles.txtnavigationSty}>
                  {translate('terms', '')}
                </Text>
              </TouchableOpacity>
              <Text>{', '}</Text>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() =>
                  navigation.navigate('WebViewScreen', {
                    type: 'PrivacyPolicy',
                    title: translate('privacyPolicy', ''),
                  })
                }>
                <Text style={styles.txtnavigationSty}>
                  {''}
                  {translate('privacyofPolicy', '')}
                </Text>
              </TouchableOpacity>
              <Text> {translate('and', '')}</Text>
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => {
                  navigation.navigate('Policy', {
                    type: 'payment_policy',
                    title: translate('paymentPolicy', ''),
                  });
                }}>
                <Text style={[styles.txtnavigationSty, {marginHorizontal: 6}]}>
                  {translate('paymentPolicy', '')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={styles.errView}>
          {checkBoxErr && (
            <Text style={styles.checkboxErr}>{checkBoxErrTxt}</Text>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button onPress={handleContinue} loading={loader} type="text">
            {translate('Continue', '')}
          </Button>
        </View>
        {/* <TouchableOpacity
          activeOpacity={0.8}
          style={[
            styles.laterTxtView,
            {
              marginTop: 15,
            },
          ]}
          onPress={() => {
            if (!click) {
              setCheckBoxErr(true);
              setCheckBoxErrTxt(
                'Please check the agreement before proceeding.',
              );
              return;
            } else if (name && lastName) {
              dispatch(
                setUserProfileData({
                  ...userProfileData, // Retain existing userProfileData properties
                  firstName: name, // Update the firstName
                  lastName: lastName, // Update the lastName
                }) as any,
              );
            }
            dispatch(setUserData({ ...userData, isProfileSetupOngoing: false }) as any);
            navigation.navigate('BottomTabsNavigator');
          }}>
          <Text style={styles.laterTxtSty}>{translate('later', '')}</Text>
        </TouchableOpacity> */}
      </KeyboardAwareScrollView>
    </View>
  );
};

export default AdName;
