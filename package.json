{"name": "Harbor", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "release": "cd ./android && ./gradlew clean && ./gradlew assemblerelease", "debug": "cd ./android && ./gradlew clean && ./gradlew assembledebug", "installDebug": "cd ./android && ./gradlew clean && ./gradlew installdebug", "updateIOS": "npx hot-updater deploy -p ios -t 1.0.0 -f", "updateAndroid": "npx hot-updater deploy -p android -t 1.0.0 -f"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@hot-updater/react-native": "^0.16.2", "@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-async-storage/async-storage": "^2.0.0", "@react-native-clipboard/clipboard": "^1.14.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "21.14.0", "@react-native-firebase/auth": "21.14.0", "@react-native-firebase/messaging": "21.14.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@sentry/react-native": "^5.35.0", "@sparkfabrik/react-native-idfa-aaid": "^1.2.0", "@stripe/stripe-react-native": "^0.40.0", "@twotalltotems/react-native-otp-input": "^1.3.11", "@types/i18n-js": "^3.8.9", "@types/lodash-es": "^4.17.12", "axios": "^1.7.7", "dayjs": "^1.11.13", "hot-updater": "^0.16.2", "i18n-js": "3.9.2", "libphonenumber-js": "^1.11.14", "lodash-es": "^4.17.21", "lottie-react-native": "^7.0.0", "moment": "^2.30.1", "react": "18.3.1", "react-content-loader": "^7.0.2", "react-hook-form": "^7.53.2", "react-native": "0.75.4", "react-native-actionsheet": "^2.4.2", "react-native-appsflyer": "^6.16.2", "react-native-awesome-gallery": "^0.4.3", "react-native-confetti-cannon": "^1.5.2", "react-native-copilot": "^3.3.3", "react-native-country-picker-modal": "^2.0.0", "react-native-device-info": "^14.0.4", "react-native-document-picker": "^9.3.1", "react-native-dotenv": "^3.4.11", "react-native-drag-sort": "^2.4.4", "react-native-element-dropdown": "^2.12.2", "react-native-event-listeners": "^1.0.7", "react-native-fast-image": "^8.6.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.20.1", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-crop-picker": "^0.42.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-material-menu": "^2.0.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-permissions": "^5.2.1", "react-native-persona": "^2.9.3", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-ratings": "^8.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.15.4", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "^3.34.0", "react-native-simple-toast": "^3.3.1", "react-native-svg": "^15.11.2", "react-native-swipeable-list": "^0.1.2", "react-native-swiper": "^1.6.0", "react-native-switch": "^1.5.1", "react-native-vector-icons": "^10.2.0", "react-native-version-check": "^3.5.0", "react-native-video": "^6.7.0", "react-native-webview": "^13.13.5", "react-redux": "^9.1.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "rn-range-slider": "^2.2.2", "socket.io-client": "^4.8.1", "yarn": "^1.22.22", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@hot-updater/metro": "0.16.2", "@hot-updater/supabase": "0.16.2", "@react-native/babel-preset": "0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@types/react": "^18.2.6", "@types/react-native-actionsheet": "^2.4.7", "@types/react-native-push-notification": "^8.1.4", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.5.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}