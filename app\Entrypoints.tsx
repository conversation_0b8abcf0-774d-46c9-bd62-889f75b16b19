/* eslint-disable @typescript-eslint/no-unused-vars */
import 'react-native-gesture-handler';
import React, {useCallback, useEffect, useState} from 'react';
import {
  LogBox,
  Platform,
  StatusBar,
  Text,
  TextInput,
  View,
  useColorScheme,
} from 'react-native';
import {Provider, useSelector} from 'react-redux';
import {PersistGate} from 'redux-persist/es/integration/react';
import {persistor, store} from './redux/store/configureStore';
import {ThemeProvider} from '@react-navigation/native';
import {CTopNotify} from './components/CTopNotify';
import {BaseStyles} from './config/theme';
import VersionCheck from 'react-native-version-check';
import Navigator from './navigation';
import {initTranslate, translate} from './lang/Translate';
import storeAction from '@redux/reducers/socket/actions';
import * as Sentry from '@sentry/react-native';
import BaseSetting from './config/setting';
import 'react-native-get-random-values';
import CustomCodePush from '@components/CustomCodePush';
import PaymentProcessing from '@components/PaymentProcessing';

import {initializeReanimated, updateUserData} from './utils/CommonFunction';
import StatusBarManager from '@components/StatusBarComponent';
import initAppsFlyer from './utils/AppsFlyerService';
import authActions from '@redux/reducers/auth/actions';
import AppUpdateModel from '@components/AppUpdateModal';

// Initialize react-native-reanimated for Android
initializeReanimated();

// const {initialization} = storeAction;

LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
LogBox.ignoreAllLogs();

if (!__DEV__) {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
}

const IOS = Platform.OS === 'ios';

Sentry.init({
  dsn: BaseSetting.sentryApiKey,
  debug: true,
  enabled: __DEV__ ? false : true,
  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // enableSpotlight: __DEV__,
});

/**
 *
 * Main entryfile for app loding
 * @export indexExport
 * @module EntryPoint
 * @return {React node showing navigator}
 * <AUTHOR>
 *
 */

// Wrapper component for PaymentProcessing that uses useSelector
const PaymentProcessingWrapper = () => {
  const isPaymentProcessing = useSelector(
    (state: any) => state.userConfig.isPaymentProcessing,
  );

  // Debug log for state changes
  useEffect(() => {
    console.log(
      'PaymentProcessingWrapper isPaymentProcessing:',
      isPaymentProcessing,
    );
  }, [isPaymentProcessing]);

  return (
    <PaymentProcessing
      visible={isPaymentProcessing}
      onClose={() =>
        store.dispatch({
          type: 'userConfig/SET_PAYMENT_PROCESSING',
          isProcessing: false,
        })
      }
    />
  );
};

interface EntryPointState {
  isCheckUpdate: boolean;
  version?: number
}

const EntryPoint = (): any => {
  const [loading, setLoading] = useState<any>(true);
  // const darkmode = store.getState().auth.darkmode;
  const userData = store.getState().auth.userData;

  const {
    socket,
    // , auth
  } = store.getState();
  // const {accessToken} = auth;
  // const scheme = useColorScheme();
  // const [darkApp, setdarkApp] = useState(darkmode);
  const [processing, setProcessing] = useState<any>(false);
  const userId = userData?.id;
  const [state, setState] = useState<EntryPointState>({ isCheckUpdate: false, version: 0 });

  useEffect(() => {
    socket?.socketObj?.disconnect();
    store.dispatch({
      type: storeAction.SET_SOCKET,
      socketObj: null,
    });
  }, []);

  useEffect(() => {
    initAppsFlyer();
  }, []);
  

  // const socketIo = socket?.socketObj;
  // useEffect(() => {
  //   // if (!isEmpty(accessToken)) {
  //   // store.dispatch(initialization() as any);
  //   // }
  // }, [socketIo, accessToken]);

  const getUserDetails = useCallback(async () => {
    updateUserData(userId || userData?.id, 'EntryPoints');
  }, [userId, userData]);
  useEffect(() => {
    if (userId) {
      getUserDetails();
    }
  }, [userId]);

  // Dark mode logic
  // useEffect(() => {
  //   if (scheme === 'dark') {
  //     setdarkApp(true);
  //     store.dispatch(authActions.setDarkmode(true) as any);
  //     handleThemeMode(true);
  //   } else {
  //     setdarkApp(false);
  //     store.dispatch(authActions.setDarkmode(false) as any);
  //     handleThemeMode(false);
  //   }
  // }, [scheme]);

  const checkAppVersion = async () => {
    try {
      const latestVersion =
        Platform.OS === 'ios'
          ? await fetch(
              'https://itunes.apple.com/in/lookup?bundleId=com.harbor.newapp',
            )
              .then(r => r.json())
              .then(res => res?.results[0]?.version)
          : await VersionCheck.getLatestVersion({
              provider: 'playStore',
              packageName: 'com.harbor.newapp',
              ignoreErrors: true,
            });

      const currentVersion = VersionCheck.getCurrentVersion();
      console.log('latestVersion ===>', latestVersion, currentVersion);
      const vCheck = latestVersion > currentVersion;
      if (vCheck) {
        // Redirect to update Modal
        setState({
          ...state,
          isCheckUpdate: true,
          version: Number(latestVersion),
        });
      } else {
        setState({
          ...state,
          isCheckUpdate: false,
        });
      }
    } catch (error) {
      console.error('Error checking app version:', error);
    }
  };

  useEffect(() => {
    if (!IOS) {
      StatusBar.setBackgroundColor('#0000', true);
    }
    checkAppVersion();
  }, []);

  const onBeforeLift = () => {
    if (store) {
      initTranslate(store);
      setLoading(false);
    }
  };

  const isDarkMode = useColorScheme() === 'dark';
  console.log('🚀 ~ EntryPoint ~ isDarkMode:', isDarkMode);

  return (
    <ThemeProvider
      value={{
        dark: false,
        colors: {
          primary: '',
          background: '',
          card: '',
          text: '',
          border: '',
          notification: '',
        },
      }}>
      <Provider store={store}>
        <PersistGate
          loading={
            <View style={{...BaseStyles.flexCenter}}>
              {/* <ActivityIndicator /> */}
            </View>
          }
          persistor={persistor}
          onBeforeLift={onBeforeLift}>
          <StatusBarManager />
          {loading ? (
            <View style={{...BaseStyles.flexCenter}}>
              {/* <ActivityIndicator /> */}
            </View>
          ) : (
            <Navigator />
          )}
          <CustomCodePush />
          {processing && (
            <CTopNotify title={translate('installingUpdates', null)} />
          )}
          <AppUpdateModel
            isVisible={state.isCheckUpdate}
            onUpdate={() => {
              setState({...state, isCheckUpdate: false});
            }}
          />
          <PaymentProcessingWrapper />
        </PersistGate>
      </Provider>
    </ThemeProvider>
  );
};

// eslint-disable-next-line curly
if ((Text as any).defaultProps == null) (Text as any).defaultProps = {};
(Text as any).defaultProps.allowFontScaling = false;
(Text as any).defaultProps = (Text as any).defaultProps || {};
(Text as any).defaultProps.allowFontScaling = false;
(TextInput as any).defaultProps = (TextInput as any).defaultProps || {};
(TextInput as any).defaultProps.allowFontScaling = false;

export default EntryPoint;

// export default HotUpdater.wrap({
//   source: 'https://eztyxzbdxbeenlaulzxd.supabase.co/functions/v1/update-server',
//   requestHeaders: {
//     // if you want to use the request headers, you can add them here
//   },
//   fallbackComponent: ({ progress, status }) => (
//     <View
//       style={{
//         flex: 1,
//         padding: 20,
//         borderRadius: 10,
//         justifyContent: 'center',
//         alignItems: 'center',
//         backgroundColor: 'rgba(0, 0, 0, 0.5)',
//       }}
//     >
//       {/* You can put a splash image here. */}

//       <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>
//         {status === 'UPDATING' ? 'Updating...' : 'Checking for Update...'}
//       </Text>
//       {progress > 0 ? (
//         <Text style={{ color: 'white', fontSize: 20, fontWeight: 'bold' }}>
//           {Math.round(progress * 100)}%
//         </Text>
//       ) : null}
//     </View>
//   ),
// })(EntryPoint);
