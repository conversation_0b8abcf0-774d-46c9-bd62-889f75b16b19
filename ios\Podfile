# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

  def node_require(script)
    # Resolve script with node to allow for hoisting
    require Pod::Executable.execute_command('node', ['-p',
      "require.resolve(
        '#{script}',
        {paths: [process.argv[1]]},
      )", __dir__]).strip
  end

  node_require('react-native-permissions/scripts/setup.rb')


# pod 'Firebase', :modular_headers => true
# pod 'FirebaseCore', :modular_headers => true
# pod 'GoogleUtilities', :modular_headers => true
# pod 'FirebaseCoreInternal', :modular_headers => true
# pod 'GTMSessionFetcher', :modular_headers => true
# pod 'AppAuth', :modular_headers => true
# pod 'Google-Maps-iOS-Utils', '4.2.2'

platform :ios, '13.4'
prepare_react_native_project!
# use_modular_headers!

setup_permissions([
  'Camera',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  # 'Notifications',
  'PhotoLibrary',
  'AppTrackingTransparency',
])


linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'Harbor' do
  config = use_native_modules!

  use_frameworks! :linkage => :static
  $RNFirebaseAsStaticFramework = true
  pod 'Firebase/Messaging'
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
  pod 'RNFS', :path => '../node_modules/react-native-fs'
  pod 'react-native-safe-area-context', :path => '../node_modules/react-native-safe-area-context'
  target 'HarborTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => false
    )

    # Bitcode stripping function
    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!

    def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
      puts "Stripping bitcode: #{command}"
      system(command)
    end

    # Paths to frameworks to strip bitcode from
    framework_paths = [
      "Pods/LogRocket/LogRocket.xcframework/ios-arm64/LogRocket.framework/LogRocket",
      "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/Versions/Current/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-maccatalyst/hermes.framework/hermes"
    ]

    # Strip bitcode from specified frameworks
    framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    end
  end
end
