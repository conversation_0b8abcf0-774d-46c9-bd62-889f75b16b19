import React, { useEffect, useRef } from 'react';
import Animated, {
  cancelAnimation,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import GetStyles from './styles';
import { Platform, View } from 'react-native';

const AnimatedView = props => {
  const styles = GetStyles();
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(30);
  const timerRef = useRef(null);

  const { children, style = {} } = props;

  useEffect(() => {
    // Use a small delay on Android to ensure the component is fully mounted
    const delay = Platform.OS === 'android' ? 50 : 0;

    // @ts-ignore - timer type issues
    timerRef.current = setTimeout(() => {
      opacity.value = withDelay(
        Platform.OS === 'android' ? 150 : 300,
        withTiming(1, { duration: Platform.OS === 'android' ? 300 : 400 })
      );
      translateY.value = withDelay(
        Platform.OS === 'android' ? 100 : 300,
        withTiming(0, { duration: Platform.OS === 'android' ? 300 : 400 })
      );
    }, delay);

    return () => {
      // Clean up animations and timers on unmount
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Cancel any in-flight animations
      cancelAnimation(opacity);
      cancelAnimation(translateY);
    };
  }, [opacity, translateY]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ translateY: translateY.value }],
    };
  });

  // For Android, wrap in a View for better stability
  if (Platform.OS === 'android') {
    return (
      <View style={[styles.mainContainer, style]}>
        <Animated.View style={[{ flex: 1 }, animatedStyle]}>
          {children}
        </Animated.View>
      </View>
    );
  }

  // For iOS, use the normal animation approach
  return (
    <Animated.View
      style={[styles.mainContainer, style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

export default AnimatedView;
