import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { Platform, StyleSheet } from 'react-native';

const IOS = Platform.OS === 'ios';


const styles = StyleSheet.create({
  mainCon: {
    width: '100%',
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  greenDot: {
    height: 13,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    width: 13,
    backgroundColor: BaseColors.green,
    borderRadius: 25,
    borderWidth: 3,
    borderColor: BaseColors.white,
  },
  badgeCount: {
    justifyContent: 'center',
    alignItems: 'center',
    // width: 20,
    // height: 20,
    position: 'absolute',
    right: 0,
    // top: -10,
    // zIndex: 999,
    backgroundColor: BaseColors.primary,
    borderRadius: 20,
    paddingLeft: 2,
  },
  badgeCountLeft: {
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    width: 20,
    height: 20,
    position: 'absolute',
    right: 37,
    top: -5,
    zIndex: 999,
    backgroundColor: BaseColors.primary,
    borderRadius: 20,
    // paddingLeft: 2,
  },
  badgeStyle: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 10,
    color: BaseColors.white,
    lineHeight: 15,
  },
  rowContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  mrTop10: {},
  titleCon: {
    // width: '80%',
    flex: 4,
    height: 40,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  titleTxt: {
    textAlign: 'center',
    fontSize: 22,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.titleColor,
    paddingBottom: IOS ? 0 : 4,
    paddingTop: IOS ? 7 : 0,
  },
  userDetails: {flexDirection: 'row', alignItems: 'center', marginLeft: 0},
  profilePic: {width: 40, height: 40, borderRadius: 20, marginRight: 10},
  userName: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textColor,
  },
  lastSeen: {
    fontSize: 11,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
  },
  userNameViewText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textColor,
    textTransform: 'capitalize',
    flex: 1,
  },
  defaultIconSty: {
    fontSize: 22,
    color: BaseColors.titleColor,
  },
  leftIconStyle: {
    height: 40,
    flex: 1,
    // alignItems: 'center',
    justifyContent: 'center',
  },
  logoStyle: {
    width: 31,
    height: 39,
  },
  rightIconSty: {
    fontSize: 25,
    color: BaseColors.primary,
  },
  rTxt: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 13,
  },
  locLable: {
    textAlign: 'center',
    fontSize: 10,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.unfocused,
  },
  locTxt: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.black,
    marginTop: 5,
    maxWidth: 150,
  },
  clrsty: {
    fontSize: 12,
    color: BaseColors.errRed,
    fontFamily: FontFamily.OpenSansBold,
  },
});

export default styles;
