import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  BackHandler,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import { Images } from '../../config/images';
// import Toast from 'react-native-simple-toast';
import authActions from '@redux/reducers/auth/actions';
import { translate } from '@language/Translate';
import auth from '@react-native-firebase/auth';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import FastImage from 'react-native-fast-image';
import Button from '@components/UI/Button';
import { BaseColors } from '@config/theme';
import { isIOS, updateReview } from '@app/utils/CommonFunction';
import { useRedux } from '@components/UseRedux';

interface OTPVerifyProps {
  navigation: any;
  route: {
    params: {
      countryCode: string;
      countryName: string;
      phoneNumber: string;
      isFor: string;
      editData?: any;
      name?: string;
      date?: string;
      confirmationResult?: any;
      code?: any;
      type?: string;
      isUserExists?: boolean;
      flagCode?: string
    };
  };
}

interface PhoneData {
  countryCode: string;
  phoneNumber: string;
  flagCode?: string;
  fcmToken: string;
  fcmPlatform: string;
}

interface GoogleData {
  googleId: string;
  fcmToken: string;
  fcmPlatform: string;
}

interface AppleData {
  appleId: string;
  fcmToken: string;
  fcmPlatform: string;
}

export default function OTPVerify({
  route,
  navigation,
}: OTPVerifyProps): React.FC<OTPVerifyProps> {
  const otpInput = useRef(null);
  const [otp, setOtp] = useState('');
  const [loader, setLoader] = useState<boolean>(false);
  const { dispatch, useAppSelector } = useRedux();

  const { fcmToken } = useAppSelector((s: any) => s.notification);
  const [resendLoader, setResendLoader] = useState<boolean>(false);

  const { confirmationResult, code, phoneNumber, flagCode,  type, isUserExists } =
    route.params;

  const [timer, setTimer] = useState(180); // 3 minutes in seconds

  // Handle back button
  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // Timer logic
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => setTimer(prev => prev - 1), 1000);
      return () => clearInterval(interval); // Clear interval on unmount
    }
  }, [timer]);

  // Function to reset timer
  const resetTimer = () => {
    setTimer(180); // Restart the timer to 3 minutes
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  type Data = PhoneData | GoogleData | AppleData;

  const userLoginSingup = async () => {
    try {
      let data: Data | undefined;

      if (type === 'phone') {
        data = {
          countryCode: `+${code}`,
          phoneNumber: phoneNumber,
          flagCode: flagCode,
          fcmToken: fcmToken || '',
          fcmPlatform: isIOS() ? 'ios' : 'android',
        };
      }
      const response = await getApiData({
        endpoint: isUserExists
          ? BaseSetting.endpoints.signin
          : BaseSetting.endpoints.signUp,
        method: 'POST',
        data: data,
      });
      console.log('🚀 ~ checkUser ~ response:', response);
      if (response?.status) {
        if (isUserExists) {
          updateReview('daily_login', response?.data?.user?.id);
        }
        const user = response?.data?.user || {};
        dispatch(authActions.setAccessToken(response?.data?.token) as any);
        console.log(
          '🚀 ~ userLoginSingup ==>',
          response?.data?.token,
          user?.isSkipped,
          !user?.isProfileSet,
        );
        if (
          !isUserExists ||
          (response?.data?.token && !user?.isSkipped && !user?.isProfileSet)
        ) {
          dispatch(authActions.setUserData({ ...response?.data?.user, isProfileSetupOngoing: true }) as any);
          navigation.replace('AddName');
        } else {
          dispatch(authActions.setUserData(response?.data?.user) as any);
          navigation.replace(
            user?.isProfileSet || user?.isSkipped
              ? 'BottomTabsNavigator'
              : 'ProfileSetUp',
          );
        }
      } else {
        // Toast.show(
        //   response?.message ||
        //   'Something went wrong while verification, please try again later!',
        //   Toast.SHORT,
        // );
      }
      setLoader(false);
    } catch (e) {
      console.log('🚀 ~ validateAndProceed ~ e:', e, `+${code}${phoneNumber}`);
      // Toast.show(
      //   'Something went wrong while verification, please try again later!',
      //   Toast.BOTTOM,
      // );
      setLoader(false);
      return { status: false };
    }
  };

  const resendOTP = async () => {
    if (resendLoader) {return;}

    setResendLoader(true);
    try {
      // Assuming you have phoneNumber stored from the previous screen
      const verificationId = await auth().signInWithPhoneNumber(
        `+${code}${phoneNumber}`,
        true,
      );
      console.log('verificationId ==> ', verificationId);

      // Reset timer and disable resend
      setResendLoader(false);
      resetTimer();

      // Toast.show('OTP sent successfully!', Toast.SHORT);
    } catch (error) {
      console.log('🚀 ~ Error resending OTP:', `+${code}${phoneNumber}`, error);
      // Toast.show('Failed to resend OTP. Please try again.', Toast.SHORT);
    } finally {
      setLoader(false);
    }
  };

  const verifyOTP = async (code: any = undefined) => {
    setLoader(true);
    try {
      const userCredential = await confirmationResult.confirm(code || otp);
      console.log('🚀 ~ OTP verified successfully!', userCredential);
      userLoginSingup();
    } catch (e) {
      setLoader(false);
      // Toast.show(
      //   'Something went wrong while verification, please try again later!',
      //   Toast.SHORT,
      // );
      console.log('🚀 ~ Error verifying OTP:', e);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: BaseColors.white }}>
      {/* <StatusBar barStyle="dark-content" backgroundColor={BaseColors.white} /> */}
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <View style={styles.mainImgView}>
          <View style={styles.imgView}>
            <FastImage
              source={Images.harborLogo}
              resizeMode={FastImage.resizeMode.contain} // Ensure proper scaling
              style={{ width: '100%', height: '100%' }} // Match the image's natural dimensions
            />
          </View>
        </View>
        <View style={styles.titleViewSty}>
          <Text style={styles.titleTxtSty}>{translate('otptitle', '')}</Text>
          <TouchableOpacity
            onPress={() => {
              navigation.replace('AuthScreen', {
                type: 'login',
                code,
                phoneNumber,
              });
            }}
            activeOpacity={0.9}
            style={{ flexDirection: 'row', alignSelf: 'center' }}>
            <Text style={styles.changePhnSty}>
              {translate('changephonenumber', '')}
            </Text>
            <View>
              <Text style={styles.editTxtSty}> - {translate('edit', '')}</Text>
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.otpBoxSty}>
          <OTPInputView
            ref={otpInput}
            pinCount={6}
            style={styles.otpInputView}
            onCodeChanged={(cd: any) => setOtp(cd)}
            autoFocusOnLoad
            codeInputFieldStyle={styles.codeInputFieldStyle}
            codeInputHighlightStyle={styles.codeInputHighlightStyle}
            onCodeFilled={(cd: any) => {
              verifyOTP(cd);
            }}
          />
          <View style={styles.sendCodeSty}>
            <Text style={styles.sendcodeTxtSty}>
              {translate('sendcode', '')} {formatTime(timer)}
            </Text>
            {timer <= 0 ? (
              <TouchableOpacity onPress={resendOTP} style={styles.sendAgain}>
                <Text style={styles.sendagainTxtSty}>
                  {translate('sendagain', '')}
                </Text>
                {resendLoader && (
                  <ActivityIndicator color={BaseColors.primary} size={15} />
                )}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <View style={styles.btnViewSty}>
          <Button
            type="text"
            onPress={() => {
              // navigation.navigate('ProfileSetUp');
              verifyOTP();
            }}
            loading={loader}>
            {translate('Continue', '')}
          </Button>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
}
