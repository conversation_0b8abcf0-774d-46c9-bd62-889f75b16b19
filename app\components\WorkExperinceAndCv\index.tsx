
import React, { useState } from 'react';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import { translate } from '@language/Translate';
import moment from 'moment';
import { BaseColors } from '@config/theme';
// import FastImage from 'react-native-fast-image';
// import FIcon from 'react-native-vector-icons/FontAwesome'; // Replace with your specific icon library if different
import { CustomIcon } from '@config/LoadIcons';
import { isEmpty } from 'lodash-es';
import { useSelector } from 'react-redux';

interface Experience {
  id?: number;
  company: string;
  designation: string;
  startDate?: string;
  endDate?: string;
  setEditExperince?: any;
}

interface WorkExperinceAndCvProps {
  experienceList: Experience[];
  setExperienceList: any;
  reviewType: any;
  setPreExperince: any;
  setOpenBottomSheet: any;
  preExperince: any;
  refRBSheet: any;
  setEdit: any;
  edit: any;
  editExperince: any;
  setEditExperince: any;
  setEditCertificate: any;
  userDetails: any;
  cvFile: any;
  navigation: any;
}

const WorkExperinceAndCv: React.FC<WorkExperinceAndCvProps> = ({
  reviewType,
  preExperince,
  userDetails,
  cvFile,
  navigation,
}) => {

  const { selectPosition, userData } = useSelector((state: any) => state.auth); // Use your RootState type

  const [showAll, setShowAll] = useState(false);
  const [truncatedStates, setTruncatedStates] = useState<{
    [key: number]: boolean;
  }>({}); // Track truncated state for each experience

  // Toggle function that only affects the clicked experience
  const toggleText = (index: number) => {
    setTruncatedStates(prev => ({
      ...prev,
      [index]: !prev[index], // Toggle the truncated state for the specific index
    }));
  };

  const visibleExperiences = showAll
    ? reviewType === 'reviewbyEmployer'
      ? userDetails?.workExperience
      : preExperince
    : reviewType === 'reviewbyEmployer'
      ? userDetails?.workExperience?.slice(0, 1)
      : preExperince?.slice(0, 2);

  const experince = preExperince || userDetails?.workExperience;

  const getPreview = () => {
    // Check for image files
    if (
      /\.(jpg|jpeg|png|gif)$/i.test(
        cvFile?.filePath ||
        cvFile ||
        cvFile?.fileName ||
        cvFile ||
        userDetails?.cv,
      )
    ) {
      navigation.navigate('GalleryView', {
        images: [
          cvFile?.filePath ||
          cvFile ||
          cvFile?.fileName ||
          cvFile ||
          userDetails?.cv,
        ],
        index: 0,
      });
    }
    // Check for document files
    else if (
      /\.(pdf|docx)$/i.test(
        cvFile?.filePath ||
        cvFile ||
        cvFile?.fileName ||
        cvFile ||
        userDetails?.cv,
      )
    ) {
      navigation.navigate('WebViewScreen', {
        uri: cvFile?.filePath ||
             cvFile ||
             cvFile?.fileName ||
             cvFile ||
             userDetails?.cv,
        type: 'profile',
      });
    }
    // Handle other file types if needed (e.g., show file icon)
    else {
      console.log('File type not supported for preview');
    }
  };

  return (
    <>
      <View>
        <View style={styles.card}>
          <View
            style={{
              borderBottomWidth: 1,
              borderColor: BaseColors?.borderColor,
              marginBottom: 5,
            }}>
            <Text style={styles?.titleTxtSty}>
              {translate('experinceandCv', '')}
            </Text>
          </View>

          {visibleExperiences?.length > 0 ? (
            <>
              {visibleExperiences.map((experience, index) => {
                const isTruncated =
                  truncatedStates[index] !== undefined
                    ? truncatedStates[index]
                    : true; // Default to true (truncated)

                const maxLength = 80; // Set the maximum length for truncated text
                const description = experience?.description || '';
                const isDescriptionLong = description.length > maxLength; // Check if description is long enough to truncate

                const visibleText =
                  isTruncated && isDescriptionLong
                    ? description.slice(0, maxLength) + '...'
                    : description;
                return (
                  <View key={experience.id || index} style={styles.cardRow}>
                    <View style={{ marginTop: reviewType === 'review' ? 0 : 5 }}>
                      <Text numberOfLines={1} style={styles.companyNameSty}>
                        {experience.company}
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          numberOfLines={3}
                          style={[
                            styles.experinceCardDiscrpition,
                            {
                              width: Dimensions.get('screen').width / 2.6,
                            },
                          ]}>
                          {/* {experience.designation} */}

                          {experience?.docTitle || '-'}
                        </Text>
                        <Text style={[styles.experinceCardDiscrpition]}>
                          {experience?.startDate
                            ? moment(experience?.startDate).format('M/D/YYYY')
                            : 'N/A'}{' '}
                          -{' '}
                          {experience?.endDate
                            ? moment(experience?.endDate).format('M/D/YYYY')
                            : translate('present', '')}
                        </Text>
                      </View>
                      <View
                        style={
                          {
                            // justifyContent: 'center',
                            // alignItems: 'flex-start',
                          }
                        }>
                        <>
                          {/* <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginTop: 10,
                            }}>
                            <View
                              style={{
                                height: 8,
                                width: 8,
                                borderRadius: 6,
                                backgroundColor: BaseColors.primary,
                              }}
                            />
                            <Text
                              style={[
                                styles.companyNameSty,
                                {fontSize: 14, marginLeft: 10},
                              ]}>
                              {translate('Title', '')}
                            </Text>
                          </View>
                          <Text style={[styles.discriptionSty]}>
                            {experience?.docTitle || '-'}
                          </Text> */}
                          {/* <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginTop: 10,
                            }}>
                            <View
                              style={{
                                height: 8,
                                width: 8,
                                borderRadius: 6,
                                backgroundColor: BaseColors.primary,
                              }}
                            />
                            <Text
                              style={[
                                styles.companyNameSty,
                                {fontSize: 14, marginLeft: 10},
                              ]}>
                              {translate('profileDiscription', '')}
                            </Text>
                          </View> */}
                          <View>
                            <Text style={[styles.discriptionSty]}>
                              {visibleText}
                            </Text>

                            {/* Show the See More button only if the description is long enough */}
                            {isDescriptionLong && (
                              <TouchableOpacity
                                onPress={() => toggleText(index)}
                                style={[
                                  styles.seeMoreButton,
                                  {
                                    alignItems: 'flex-start',
                                    marginHorizontal: 18,
                                  },
                                ]}>
                                <Text style={styles.seeMoreText}>
                                  {isTruncated
                                    ? translate('seeMore', '')
                                    : translate('seeLess', '')}
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </>
                      </View>
                    </View>
                    <View
                      style={{
                        borderBottomWidth: 0.7,
                        borderColor: BaseColors.borderColor,
                        marginTop: 10,
                      }}
                    />
                  </View>
                );
              })}
            </>
          ) : isEmpty(cvFile || userDetails?.cv) ? (
            <Text style={styles?.nodataTxt}>-</Text>
          ) : null}

          {reviewType === 'reviewbyEmployer'
            ? userDetails?.workExperience?.length > 2
            : preExperince &&
              preExperince.length > 2 && (
                <TouchableOpacity
                  onPress={() => setShowAll(!showAll)}
                  style={styles.seeMoreButton}>
                  <Text style={styles.seeMoreText}>
                    {showAll ? translate('Less', '') : translate('More', '')}
                  </Text>
                </TouchableOpacity>
              )}

          {cvFile || userDetails?.cv ? (
            <>
              {/* <View>
                <Text style={styles?.experincesty}>
                  {translate('cvResume', '')}
                </Text>
              </View> */}
              <>
                <View style={styles.cvFileContainer}>
                  {userDetails?.isCvVerified || userData?.isCvVerified ? (
                    <View style={styles.verificationSty}>
                      <CustomIcon
                        name="Vector"
                        color={BaseColors.primary}
                        size={20}
                      />
                    </View>
                  ) : null}
                  <View style={styles.fileInfoContainer}>
                    {/* <TouchableOpacity
                      onPress={() => {
                        // Check for image files
                        if (
                          /\.(jpg|jpeg|png|gif)$/i.test(
                            cvFile?.filePath ||
                              cvFile ||
                              cvFile?.fileName ||
                              cvFile ||
                              userDetails?.cv,
                          )
                        ) {
                          navigation.navigate('GalleryView', {
                            images: [
                              cvFile?.filePath ||
                                cvFile ||
                                cvFile?.fileName ||
                                cvFile ||
                                userDetails?.cv,
                            ],
                            index: 0,
                          });
                        }
                        // Check for document files
                        else if (
                          /\.(pdf|docx)$/i.test(
                            cvFile?.filePath ||
                              cvFile ||
                              cvFile?.fileName ||
                              cvFile ||
                              userDetails?.cv,
                          )
                        ) {
                          navigation.navigate('WebViewScreen', {
                            uri:
                              cvFile?.filePath ||
                              cvFile ||
                              cvFile?.fileName ||
                              cvFile ||
                              userDetails?.cv,
                            type: 'profile',
                          });
                        }
                        // Handle other file types if needed (e.g., show file icon)
                        else {
                          console.log('File type not supported for preview');
                        }
                      }}
                      style={styles.iconSty}>
                      {cvFile || cvFile?.fileName || userDetails?.cv ? (
                        // Check for image files
                        /\.(jpg|jpeg|png|gif)$/i.test(
                          cvFile || cvFile?.fileName || userDetails?.cv,
                        ) ? (
                          <FastImage
                            source={{
                              uri:
                                cvFile?.filePath ||
                                cvFile ||
                                cvFile?.fileName ||
                                cvFile ||
                                userDetails?.cv,
                            }}
                            style={{width: '100%', height: '100%'}}
                            resizeMode="contain"
                          />
                        ) : /\.(pdf|docx)$/i.test(
                            cvFile ||
                              cvFile?.filePath ||
                              cvFile?.fileName ||
                              userDetails?.cv,
                          ) ? (
                          // Check for document files (pdf, docx, etc.)
                          <FIcon
                            name="file-pdf-o" // You can replace this with the appropriate icon if needed
                            size={30}
                            color={BaseColors.primary}
                          />
                        ) : (
                          // Default for unsupported file types
                          <FIcon
                            name="file"
                            size={30}
                            color={BaseColors.primary}
                          />
                        )
                      ) : (
                        <FIcon
                          name="file"
                          size={30}
                          color={BaseColors.primary}
                        />
                      )}
                    </TouchableOpacity> */}
                    <View style={{flexDirection: 'row'}}>
                      <View style={styles.fileDetailsContainer}>
                        <Text
                          numberOfLines={1}
                          style={[styles.fileNameText, { color: userDetails?.cvTitle ? BaseColors.primary : BaseColors.textColor }]}
                          onPress={() => {
                            getPreview();
                          }}>
                          {/* {userDetails?.cvTitle
                            ? userDetails?.cvTitle
                            : typeof (cvFile || userDetails?.cv) === 'string' &&
                              (cvFile || userDetails?.cv).includes('http')
                            ? (cvFile || userDetails?.cv).split('/').pop()
                            : cvFile?.fileName || '-'} */}
                          {userDetails?.cvTitle ? userDetails?.cvTitle : translate('cvResume')}
                          {!userDetails?.cvTitle && <Text style={{ color: BaseColors.yellow }}>
                            {' '} {translate('pendingVerification', '')}
                          </Text>}
                        </Text>
                        {cvFile?.fileSize && (
                          <Text style={styles.fileSizeText}>
                            Size: {cvFile.fileSize}
                          </Text>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              </>
            </>
          ) : null}
        </View>
      </View>
    </>
  );
};

export default WorkExperinceAndCv;
