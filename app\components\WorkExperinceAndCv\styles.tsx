import { Dimensions, StyleSheet } from 'react-native';
import { BaseColors } from '../../config/theme';
import { FontFamily } from '@config/typography';

export default StyleSheet.create({
  card: {
    backgroundColor: BaseColors.inputBackground,
    padding: 16,
    borderRadius: 8,
    // borderWidth: 1,
    // borderColor: BaseColors.white20,
    marginBottom: 10,
  },
  cardRow: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // marginBottom: 8,
    backgroundColor: BaseColors?.inputBackground,
    padding: 5,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
  },
  cardSubtitle: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  cardDescription: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    marginVertical: 5,
  },
  titleTxtSty: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 10,
  },
  expSty: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    marginBottom: 10,
  },
  titleStyle: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textColor,
  },
  borderStyle: {
    borderColor: BaseColors.borderColor,
    marginLeft: 10,
  },
  crossView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  removeFileButton: {
    borderWidth: 1,
    borderColor: BaseColors.textGrey,
    borderRadius: 6,
    position: 'absolute',
    right: 0,
  },
  editIconsty: {
    borderWidth: 1,
    borderColor: BaseColors.textGrey,
    borderRadius: 6,
    position: 'absolute',
    right: 40,
    padding: 3,
  },
  seeMoreButton: {
    marginTop: 0,
    alignItems: 'center',
    // borderTopWidth: 0.7,
    paddingTop: 5,
    borderColor: BaseColors?.borderColor,
  },
  seeMoreText: {
    color: BaseColors.primary,
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
    textTransform:'capitalize',

  },
  iconViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  companyNameSty: {
    color: BaseColors.textColor,
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
    width: Dimensions.get('screen').width / 1.6,
    textTransform:'capitalize',
  },
  experinceCardDiscrpition: {
    fontSize: 13,
    color: BaseColors?.inputColor,
    fontFamily: FontFamily?.OpenSansRegular,
    lineHeight: 25,
    textTransform:'capitalize',

  },
  discriptionSty: {
    fontSize: 13,
    color: BaseColors?.inputColor,
    fontFamily: FontFamily?.OpenSansRegular,
    lineHeight: 18,
    // marginLeft: 18,
    // textTransform:'capitalize',

  },
  toggleText: {
    color: BaseColors.primary,
    fontFamily: FontFamily?.OpenSansRegular,
    fontSize: 14,
  },
  endTxtLine: {
    textAlign: 'justify',
    marginHorizontal: 20,
  },
  cvFileContainer: {
    flexDirection: 'row',
    // borderWidth: 1,
    marginTop: 15,
    marginBottom: 5,
    justifyContent: 'space-between',
    borderColor: BaseColors.inputBorderSty,
    // paddingHorizontal: 15,
    padding: 10,
    alignItems: 'center',
  },
  verificationSty: {
    paddingRight: 10,
  },
  fileInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconSty: {
    width: Dimensions.get('screen').width / 6,
    height: Dimensions.get('screen').width / 6,
    borderRadius: 10,
    borderWidth: 0.8,
    borderColor: BaseColors.borderColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileIconColor: {
    color: BaseColors.primary,
  },
  fileDetailsContainer: {
    flexDirection: 'column',
    // paddingLeft: 10,
    flex: 1,
    // width: '78%',
  },
  fileNameText: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansRegular,
    // textTransform:"capitalize"

  },
  experincesty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily?.OpenSansBold,
  },
  nodataTxt: {
    fontSize: 16,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansMedium,
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 10,
    textTransform:'capitalize',

  },
});
