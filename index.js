/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './app/Entrypoints';
import messaging from '@react-native-firebase/messaging';
import { name as appName } from './app.json';
import { LogBox } from 'react-native';
// import messaging from '@react-native-firebase/messaging';
import NotificationAction from './app/redux/reducers/notification/actions';
import { store } from './app/redux/store/configureStore';
// import {registerGlobals} from '@livekit/react-native';
// import Bugsnag from '@bugsnag/react-native';

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
  //   const type = remoteMessage?.data?.type || '';
  store.dispatch(NotificationAction.setNotificationType(remoteMessage));
});
// Bugsnag.start();
// registerGlobals();

LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
LogBox.ignoreAllLogs();
AppRegistry.registerComponent(appName, () => App);
