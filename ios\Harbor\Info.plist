<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Apps<PERSON><PERSON>erAppID</key>
	<string>id6746108776</string>
	<key>AppsFlyerDevKey</key>
	<string>CUczsiExfiWsj6yU6Dbx6C</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Harbor</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.735633804275-0pb1enuqo12kk7bucpnhql2k5eantqsa</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>theharborapp.onelink.me</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>theharborapp.onelink.me</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>harbor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>harbor.newapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>thetheharborapp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>************</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
			<key>api.theharborapp.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>dev-api.theharborapp.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>localhost</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
			<key>theharborapp.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>theharborapp.onelink.me</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>The harbor app need to access Camera for Government Id, Selfie, and Document flows.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to provide better service.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location to provide better service.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to provide better service.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allows the app to save images to your photo library.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>The harbor app need to access your photos library for change profile picture , used on Document flows and on Government Id flows.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Harbor uses tracking to improve job matching, enable referral rewards, and provide personalized experiences for seekers and employers in the marine industry.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Fonts/AntDesign.ttf</string>
		<string>Fonts/Entypo.ttf</string>
		<string>Fonts/EvilIcons.ttf</string>
		<string>Fonts/Feather.ttf</string>
		<string>Fonts/FontAwesome.ttf</string>
		<string>Fonts/FontAwesome5_Brands.ttf</string>
		<string>Fonts/FontAwesome5_Regular.ttf</string>
		<string>Fonts/FontAwesome5_Solid.ttf</string>
		<string>Fonts/FontAwesome6_Brands.ttf</string>
		<string>Fonts/FontAwesome6_Regular.ttf</string>
		<string>Fonts/FontAwesome6_Solid.ttf</string>
		<string>Fonts/Foundation.ttf</string>
		<string>Fonts/Ionicons.ttf</string>
		<string>Fonts/MaterialIcons.ttf</string>
		<string>Fonts/MaterialCommunityIcons.ttf</string>
		<string>Fonts/SimpleLineIcons.ttf</string>
		<string>Fonts/Octicons.ttf</string>
		<string>Fonts/Zocial.ttf</string>
		<string>Fonts/Fontisto.ttf</string>
		<string>Fonts/icomoon.ttf</string>
		<string>Fonts/helvetica.ttf</string>
		<string>Fonts/helvetica-bold.ttf</string>
		<string>Fonts/helvetica-medium.ttf</string>
		<string>Fonts/OpenSansBold.ttf</string>
		<string>Fonts/OpenSansExtraBold.ttf</string>
		<string>Fonts/OpenSansLight.ttf</string>
		<string>Fonts/OpenSansMedium.ttf</string>
		<string>Fonts/OpenSansRegular.ttf</string>
		<string>Fonts/OpenSansSemiBold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
