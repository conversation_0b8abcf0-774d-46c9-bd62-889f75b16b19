import { isEmpty } from 'lodash-es';
import appsFlyer from 'react-native-appsflyer';
// import Toast from 'react-native-simple-toast';

type AnyObject = Record<string, any>;

const renameKeys = (obj: AnyObject): AnyObject => {
  return Object.keys(obj).reduce((newObj: AnyObject, key: string) => {
    newObj[`af_${key}`] = obj[key]; // Add the prefix to each key
    return newObj;
  }, {});
};

// const generateEventID = (): string =>
//   `event_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

const getUTCTime = (): string => {
  const now = new Date();
  return now.toISOString(); // Get the time in UTC ISO format
};

export const trackPurchaseEvent = (
  eventName: string = '',
  values: AnyObject = {}
): void => {
  const eventValues: AnyObject = {
    ...renameKeys(values),
    af_timestamp: getUTCTime(),
  };

  if (!isEmpty(eventName)) {
    appsFlyer.logEvent(
      eventName,
      eventValues,
      (result: any) => {
        console.log('Event logged successfully: ', result);
      },
      (error?: { message?: string }) => {
        console.log('🚀 ~ trackPurchaseEvent ~ error:', error);
        console.error('Error logging event: ', error?.message ?? 'Unknown error');
      }
    );
  }
};
