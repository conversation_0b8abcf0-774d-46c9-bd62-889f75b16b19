import { BaseColors } from '@config/theme';
import { translate } from '../../lang/Translate';
import React from 'react';
import { Linking, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { isIOS } from '@app/utils/CommonFunction';
import authActions from '@redux/reducers/auth/actions';
import { useDispatch } from 'react-redux';

interface UpdateInterface {
    isVisible?: boolean
    onUpdate?: any
}


const AppUpdateModel = ({ isVisible = false, onUpdate }: UpdateInterface) => {
  const dispatch = useDispatch();
  const { setIsUpdate } = authActions;

  return (
    <Modal transparent={true} animationType={'none'} visible={isVisible}>
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <Text style={styles.title}>{translate('updateAvailable')}</Text>
          <Text style={styles.message}>{translate('versionUpdateTxt')}</Text>
          <TouchableOpacity
            onPress={() => {
              const url = isIOS() ? 'https://apps.apple.com/us/app/the-harbor-app/id6746108776' : 'https://play.google.com/store/apps/details?id=com.harbor.newapp';
              Linking.openURL(url);
              dispatch(setIsUpdate(false) as any);
              onUpdate();
            }}
            activeOpacity={0.9}
            style={styles.btnStyle}>
            <Text style={styles.modalButtonText}>{translate('UpdateNow')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // Define your styles here
  modalBackground: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'space-around',
    backgroundColor: '#00000040',
    padding: 50,
  },
  activityIndicatorWrapper: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-around',
    padding: 20,
  },
  activityIndicator: {
    alignItems: 'center',
    height: 80,
  },
  title: {
    color: BaseColors.primary,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    padding: 6,
    textAlign: 'center',
  },
  btnStyle: {
    borderRadius: 10,
    backgroundColor: BaseColors.primary,
    marginTop: 10,
    width: '100%',
  },
  modalButtonText: {
    color: BaseColors.white,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    padding: 6,
    textAlign: 'center',
    paddingHorizontal: 18,
  },
  message: {
    color: '#333',
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    padding: 6,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
});

export default AppUpdateModel;
