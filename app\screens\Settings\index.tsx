/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useRef, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import { translate } from '@language/Translate';
import { BaseColors } from '@config/theme';
import { otherSettings, payments, settings } from './staticData';
import { CustomIcon } from '@config/LoadIcons';
import LanguageModal from '@components/LanguageModal';
import { logOutCall } from '@app/utils/CommonFunction';
import Button from '@components/UI/Button';

import { initTranslate } from '@language/Translate';
import langActions from '../../redux/reducers/language/actions';
import { store } from '../../redux/store/configureStore';
import AnimatedView from '@components/AnimatedView';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { useFocusEffect } from '@react-navigation/native';
import AlertModal from '@components/AlertModal';

export default function Settings({ navigation }: {navigation: any}) {
  const dispatch = useAppDispatch();
  const { useAppSelector } = useRedux();
  const { languageData } = useAppSelector((state: any) => {
    return state.language;
  });
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });
  const [updateUrl, setUpdateUrl] = useState(false);
  const { userData } = useAppSelector(s => s.auth);
  const refRBSheet = useRef(null);

  const [openBottomSheet, setOpenBottomSheet] = useState<boolean>(false);
  const [openlogoutBottomSheet, setOpenLogoutBottomSheet] =
    useState<boolean>(false);
  const { setLanguage } = langActions;

  const [selectedLanguage, setSelectedLanguage] =
    useState<string>(languageData);

  const handleLanguageChange = () => {
    dispatch(setLanguage(selectedLanguage) as any);
    initTranslate(store, true);
    refRBSheet?.current?.close();
  };
  const handleLogout = async () => {
    await logOutCall();
    navigation.replace('AuthScreen');
    refRBSheet?.current?.close();
  };

  const isRedirect = userData?.bankAccountVerified === 'verified' ||
  userData?.bankAccountVerified === 'notVerified' ||
  userData?.bankAccountVerified === 'pending';

  async function getWalletData() {
    try {
      const endpoint = BaseSetting.endpoints.checkKyc;
      const response = await getApiData({
        endpoint,
        method: 'GET',
      });
      console.log('🚀 ~ getWalletData ~ response:', response);
      if (response?.status) {
        setUpdateUrl(response?.data);
      }
    } catch (er) {}
  }

  useFocusEffect(
    useCallback(() => {
      getWalletData();
    }, []),
  );

  return (
    <View style={styles.container}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        title={translate('settings', '')}
        leftIcon="back-arrow"
        onLeftPress={() => {
          navigation.goBack();
        }}
        onRightPress={() => {
          navigation.navigate('Settings');
        }}
        rightIconSty={{ paddingTop: 10 }}
      />

      <AnimatedView>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* {!isProfileFullyCompleted ? (
            <View>
              <SetupSteps navigation={navigation} />
            </View>
          ) : null} */}
          <View
            style={{
              ...styles.mainView,
              // marginBottom: Dimensions.get('screen').height / 9,
            }}>
            <View style={styles.settings}>
              <Text style={styles.title}>{translate('Account', '')}</Text>

              {settings.map((d, idx) => (
                <TouchableOpacity
                  key={`${idx}`}
                  onPress={() => {
                    if (d.type) {
                      if (d?.type === 'VerificationDetail' && userData?.isProfileSet === false) {
                        setModalOpen(p => ({ ...p, confirmationModal: true }));
                      } else {
                        const navigate =
                        d?.type === 'ProfileSetUp'
                          ? { redirection: 'back', type: 'edit' }
                          : {};
                        navigation.navigate(d.type, { ...navigate });
                      }
                    }
                  }}
                  style={{
                    ...styles.subCard,
                    borderBottomWidth: settings.length - 1 === idx ? 0 : 0.5,
                  }}>
                  <CustomIcon
                    name={d.icon}
                    color={BaseColors.textGrey}
                    size={20}
                    style={styles.subCardIcon}
                  />
                  <Text style={styles.titleText}>{translate(d.title, '')}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.settings}>
              <Text style={styles.title}>{translate('payments', '')}</Text>

              {payments.map((d, idx) => (
                <TouchableOpacity
                  key={`${idx}`}
                  activeOpacity={0.9}
                  onPress={() => {
                    if (d?.type === 'connectBank' && userData?.isProfileSet === false) {
                      setModalOpen(p => ({ ...p, confirmationModal: true }));
                    } else if (isRedirect && updateUrl?.url && d?.screen === 'Wallet') {
                      navigation.navigate('KycCompleteScreen', {
                        uri: updateUrl?.url || '',
                      });
                    } else {
                      navigation.navigate(d.screen, { type: d?.type });
                    }
                  }}
                  style={{
                    ...styles.subCard,
                    borderBottomWidth:
                      otherSettings.length - 1 === idx ? 0 : 0.5,
                  }}>
                  <CustomIcon
                    name={d.icon}
                    color={
                      d?.type === 'delete'
                        ? BaseColors.red
                        : BaseColors.textGrey
                    }
                    size={20}
                    style={styles.subCardIcon}
                  />
                  <Text
                    style={{
                      ...styles.titleText,
                      color:
                        d?.type === 'delete'
                          ? BaseColors.red
                          : BaseColors.textGrey,
                    }}>
                    {translate(d.title, '')}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.settings}>
              <Text style={styles.title}>{translate('support', '')}</Text>

              {otherSettings.map((d, idx) => {
                console.log('🚀 ~ {otherSettings.map ~ d:', d);
                return (
                  <TouchableOpacity
                    key={`${idx}`}
                    activeOpacity={0.9}
                    onPress={() => {
                      if (d?.isPopup === 'language') {
                        setOpenBottomSheet(true);
                        refRBSheet?.current?.open();
                      }
                      if (d?.type === 'logout') {
                        setOpenLogoutBottomSheet(true);
                        refRBSheet.current?.open();
                        return true;
                      }
                      if (d?.isPopup === 'language') {
                        null;
                      } else {
                        navigation.navigate(d.screen, { type: d?.type });
                      }
                    }}
                    style={{
                      ...styles.subCard,
                      borderBottomWidth:
                        otherSettings.length - 1 === idx ? 0 : 0.5,
                    }}>
                    <CustomIcon
                      name={d.icon}
                      color={
                        d?.type === 'delete'
                          ? BaseColors.red
                          : BaseColors.textGrey
                      }
                      size={20}
                      style={styles.subCardIcon}
                    />
                    <Text
                      style={{
                        ...styles.titleText,
                        color:
                          d?.type === 'delete'
                            ? BaseColors.red
                            : BaseColors.textGrey,
                      }}>
                      {translate(d.title, '')}
                    </Text>
                  </TouchableOpacity>
                );
              })}

              {/*
            <Button
              style={{width: '100%'}}
              disable={!loading}
              onPress={() => openPaymentSheet()}>
              {'Strip test'}
            </Button> */}
            </View>
          </View>
        </ScrollView>
        <View style={styles?.btnViewSty}>
          <Button
            onPress={() => {
              navigation.navigate('ContactUs', '');
            }}
            type="text">
            {translate('contactUs')}
          </Button>
        </View>
      </AnimatedView>
      <LanguageModal
        refRBSheet={refRBSheet}
        setOpenBottomSheet={
          openBottomSheet ? setOpenBottomSheet : setOpenLogoutBottomSheet
        }
        title={
          openBottomSheet
            ? translate('changeLanguage', '')
            : translate('Logout', '')
        }
        openLanguage={openBottomSheet ? true : false}
        cancelProp={translate('Cancel', '')}
        onClick={openBottomSheet ? handleLanguageChange : handleLogout}
        // onClick={handleLanguageChange}
        doneProp={
          openBottomSheet ? translate('Done', '') : translate('logOut', '')
        }
        showDescription={openBottomSheet ? false : true}
        selectedLanguage={selectedLanguage}
        setSelectedLanguage={setSelectedLanguage}
        Description={openBottomSheet ? null : translate('askingLogout', '')}
        deleteSty={openBottomSheet ? false : true}
      />
      <AlertModal
        image
        title={translate('complete', '')}
        visible={modalOpen.confirmationModal}
        setVisible={(val: any) =>
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
        }
        lottieViewVisible
        btnYPress={() => {
          navigation.navigate('ProfileSetUp');
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
        }}
        loader={modalOpen?.loader}
        btnYTitle={translate('letsdo')}
        confirmation
        // completeProfile
        titlesty={{ textAlign: 'center' }}
        description={translate('postDescription', '')}
        btnNTitle={translate('maybe')}
        btnNPress={() => {
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
        }}
      />
    </View>
  );
}
